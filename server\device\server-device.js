/**
 * 服务器设备管理模块 - 完整拆分版本
 * 包含所有设备管理相关的API和功能
 * 对应原始文件第339-1850行的完整内容，包含以下API：
 * - GET /api/device/list - 获取设备列表
 * - POST /api/device/force-status - 强制更新设备状态
 * - POST /api/device/register - 设备注册
 * - GET /api/device/:deviceId/commands - 获取待执行命令
 * - POST /api/device/:deviceId/result - 上报脚本执行结果
 * - DELETE /api/device/:id - 断开设备连接
 * - DELETE /api/device/:id/delete - 删除设备记录
 * - POST /api/device/:deviceId/disconnect - 主动断开连接
 * - POST /api/device/:deviceId/apps - 设备应用信息上报
 * - GET /api/device/:deviceId/apps - 获取设备应用信息
 * - GET /api/device/check-stop - 检查停止信号
 * - POST /api/device/script-stopped - 通知脚本已停止
 * - POST /api/device/:deviceId/script-status - 接收设备脚本状态
 * - POST /api/device/:deviceId/app-status - 接收设备应用状态
 * - POST /api/device/clear-chat-records-response - 接收设备清空私聊记录响应
 * - POST /api/device/script-completion-ack - 接收设备脚本完成确认响应
 */

// 设备管理模块设置函数
async function setupServerDevice(app, io, coreData, authData) {
  console.log('🔧 设置设备管理模块...');
  
  const {
    pool,
    devices,
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    recentlyDisconnectedDevices,
    xiaohongshuLogService,
    xianyuLogService,
    xianyuChatService,
    throttledLog
  } = coreData;

  // 设备停止信号存储
  const deviceStopSignals = new Map();

  // 活跃用户跟踪
  const activeUsers = new Map(); // 存储最近活跃的用户信息

  const { authenticateToken } = authData;

  // 引入用户隔离中间件和工具
  const { userIsolationMiddleware } = require('../middleware/userIsolation');
  const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
  const PermissionValidator = require('../utils/PermissionValidator');

  // 创建数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 初始化活跃用户跟踪
  app.locals.activeUsers = activeUsers;



  // 管理员分配设备给用户API - 新增功能
  app.post('/api/device/assign-to-user', authenticateToken, async (req, res) => {
    try {
      const { deviceId, targetUserId, ipAddress } = req.body;
      const currentUserId = req.user.id;

      console.log(`[设备分配] 用户${currentUserId}请求将设备分配给用户${targetUserId}`);

      // 检查当前用户是否为管理员（可选，根据需求调整）
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以分配设备'
        });
      }

      if (!targetUserId) {
        return res.status(400).json({
          success: false,
          message: '目标用户ID不能为空'
        });
      }

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      // 验证目标用户是否存在
      const [targetUsers] = await pool.execute(
        'SELECT id, username FROM users WHERE id = ?',
        [targetUserId]
      );

      if (targetUsers.length === 0) {
        return res.status(404).json({
          success: false,
          message: `用户ID ${targetUserId} 不存在`
        });
      }

      const targetUser = targetUsers[0];

      let targetDevice = null;

      // 根据deviceId或ipAddress查找设备
      if (deviceId) {
        const [devices] = await pool.execute(
          'SELECT device_id, user_id, device_name, device_info FROM devices WHERE device_id = ?',
          [deviceId]
        );
        if (devices.length > 0) {
          targetDevice = devices[0];
        }
      } else if (ipAddress) {
        const [devices] = await pool.execute(
          `SELECT device_id, user_id, device_name, device_info
           FROM devices
           WHERE JSON_EXTRACT(device_info, '$.ipAddress') = ?`,
          [ipAddress]
        );
        if (devices.length > 0) {
          targetDevice = devices[0];
        }
      } else {
        return res.status(400).json({
          success: false,
          message: '必须提供设备ID或IP地址'
        });
      }

      if (!targetDevice) {
        return res.status(404).json({
          success: false,
          message: deviceId ? `设备 ${deviceId} 不存在` : `IP地址 ${ipAddress} 的设备不存在`
        });
      }

      // 检查设备是否已属于目标用户
      if (targetDevice.user_id === targetUserId) {
        return res.json({
          success: true,
          message: `设备 "${targetDevice.device_name}" 已属于用户 ${targetUser.username}`,
          deviceId: targetDevice.device_id,
          deviceName: targetDevice.device_name,
          targetUser: targetUser.username
        });
      }

      console.log(`[设备分配] 将设备${targetDevice.device_id}(${targetDevice.device_name})分配给用户${targetUserId}(${targetUser.username})`);

      // 执行设备分配
      await pool.execute(
        'UPDATE devices SET user_id = ?, last_seen = NOW() WHERE device_id = ?',
        [targetUserId, targetDevice.device_id]
      );

      // 同时更新设备的应用信息
      await pool.execute(
        'UPDATE device_apps SET user_id = ? WHERE device_id = ?',
        [targetUserId, targetDevice.device_id]
      );

      console.log(`✅ 设备分配完成: 设备${targetDevice.device_id}已分配给用户${targetUserId}(${targetUser.username})`);

      res.json({
        success: true,
        message: `成功将设备 "${targetDevice.device_name}" 分配给用户 ${targetUser.username}`,
        deviceId: targetDevice.device_id,
        deviceName: targetDevice.device_name,
        fromUserId: targetDevice.user_id,
        targetUserId: targetUserId,
        targetUsername: targetUser.username
      });

    } catch (error) {
      console.error('设备分配失败:', error);
      res.status(500).json({
        success: false,
        message: '分配失败: ' + error.message
      });
    }
  });

  // 获取未分配设备列表API
  app.get('/api/device/unassigned', authenticateToken, async (req, res) => {
    try {
      const currentUserId = req.user.id;

      // 检查当前用户是否为管理员
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以查看未分配设备'
        });
      }

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      // 查找未分配的设备（user_id为NULL或0）
      const [unassignedDevices] = await pool.execute(`
        SELECT
          device_id,
          device_name,
          device_info,
          status,
          last_seen,
          created_at
        FROM devices
        WHERE user_id IS NULL OR user_id = 0
        ORDER BY last_seen DESC
      `);

      res.json({
        success: true,
        data: unassignedDevices.map(device => ({
          ...device,
          device_info: typeof device.device_info === 'string'
            ? JSON.parse(device.device_info)
            : device.device_info
        }))
      });

    } catch (error) {
      console.error('获取未分配设备失败:', error);
      res.status(500).json({
        success: false,
        message: '获取失败: ' + error.message
      });
    }
  });





  // 获取设备列表API (原始文件第339行) - 已添加用户隔离
  app.get('/api/device/list', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;
      let deviceList = [];

      if (pool) {
        // 数据库可用，从数据库获取设备记录（自动添加用户过滤）
        try {
          const [dbDevices] = await dbEnhancer.executeWithUserFilter(`
            SELECT device_id, device_name, device_info, status, last_seen, created_at
            FROM devices
            ORDER BY last_seen DESC
          `, [], userId);

          console.log(`[设备列表] 用户${userId}查询到${dbDevices.length}个设备`);

          // 获取内存中的连接状态（只获取属于当前用户的设备）
          const connectedDevices = new Map();
          for (const [socketId, device] of devices) {
            // 验证设备是否属于当前用户
            const hasPermission = await permissionValidator.validateDeviceOwnership(device.deviceId, userId);
            if (hasPermission) {
              connectedDevices.set(device.deviceId, {
                status: device.status,
                connectedAt: device.connectedAt,
                lastSeen: device.lastSeen || device.connectedAt,
                socketId: socketId
              });
            }
          }

          // 合并数据库记录和连接状态
          deviceList = dbDevices.map(dbDevice => {
            const connectedDevice = connectedDevices.get(dbDevice.device_id);
            let deviceInfo = dbDevice.device_info;

            // 解析JSON格式的device_info
            if (typeof deviceInfo === 'string') {
              try {
                deviceInfo = JSON.parse(deviceInfo);
              } catch (e) {
                deviceInfo = {};
              }
            }

            // 设备状态基于实时连接状态，而不是数据库历史状态
            const realTimeStatus = connectedDevice ? connectedDevice.status : 'offline';

            return {
              deviceId: dbDevice.device_id,
              deviceName: dbDevice.device_name,
              deviceInfo: deviceInfo,
              status: realTimeStatus, // 使用实时连接状态
              lastActiveTime: connectedDevice ? connectedDevice.lastSeen : dbDevice.last_seen,
              createdAt: dbDevice.created_at,
              deviceIP: deviceInfo?.ipAddress || '未知',
              isConnected: !!connectedDevice // 是否当前连接
            };
          });

          // 使用节流日志，避免频繁输出设备列表查询结果
          throttledLog('device_list_query', `设备列表查询完成: 数据库中${dbDevices.length}个设备，当前连接${connectedDevices.size}个设备`);
        } catch (dbError) {
          console.error('数据库查询失败，回退到内存模式:', dbError);
          // 数据库查询失败，回退到内存模式
          deviceList = Array.from(devices.values()).map(device => ({
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            deviceInfo: device.deviceInfo,
            status: device.status,
            lastActiveTime: device.lastSeen || device.connectedAt,
            createdAt: device.connectedAt,
            deviceIP: device.deviceInfo?.ipAddress || '未知',
            isConnected: true
          }));
        }
      } else {
        // 数据库不可用，使用内存模式
        deviceList = Array.from(devices.values()).map(device => ({
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          deviceInfo: device.deviceInfo,
          status: device.status,
          lastActiveTime: device.lastSeen || device.connectedAt,
          createdAt: device.connectedAt,
          deviceIP: device.deviceInfo?.ipAddress || '未知',
          isConnected: true
        }));
        // 使用节流日志，避免频繁输出内存模式查询结果
        throttledLog('device_list_memory', `设备列表查询完成（内存模式）: 当前连接${deviceList.length}个设备`);
      }

      res.json({
        success: true,
        data: deviceList
      });

    } catch (error) {
      console.error('获取设备列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取设备列表失败: ' + error.message
      });
    }
  });

  // 强制更新设备状态API (原始文件第439行) - 已添加用户隔离
  app.post('/api/device/force-status', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { deviceId, status } = req.body;
      const userId = req.currentUserId;

      if (!deviceId || !status) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      // 验证设备所属权
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权操作此设备'
        });
      }

      console.log(`[用户${userId}] 强制更新设备状态: ${deviceId} -> ${status}`);

      // 1. 更新内存中的设备状态
      if (devices.has(deviceId)) {
        const device = devices.get(deviceId);
        device.status = status;
        device.lastSeen = new Date();
        devices.set(deviceId, device);
        console.log(`内存中设备状态已更新: ${deviceId} -> ${status}`);
      } else {
        console.log(`设备 ${deviceId} 在内存中不存在，跳过内存更新`);
      }

      // 2. 更新数据库中的设备状态（如果数据库可用）
      if (pool) {
        try {
          const [result] = await pool.execute(`
            UPDATE devices SET status = ?, last_seen = NOW()
            WHERE device_id = ? AND user_id = ?
          `, [status, deviceId, userId]);

          if (result.affectedRows > 0) {
            console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
          } else {
            console.log(`设备 ${deviceId} 在数据库中不存在或无权访问`);
          }
        } catch (dbError) {
          console.error('数据库更新设备状态失败:', dbError);
          // 数据库更新失败不影响响应，因为内存已更新
        }
      }

      // 3. 通过WebSocket广播设备状态更新
      io.emit('device_status_update', {
        deviceId: deviceId,
        status: status,
        lastSeen: new Date()
      });

      console.log(`设备状态强制更新完成: ${deviceId} -> ${status}`);

      res.json({
        success: true,
        message: '设备状态更新成功',
        data: {
          deviceId: deviceId,
          status: status,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('强制更新设备状态失败:', error);
      res.status(500).json({
        success: false,
        message: '强制更新设备状态失败: ' + error.message
      });
    }
  });

  // 更新设备状态函数 (原始文件第515行)
  async function updateDeviceStatus(deviceId, status) {
    console.log(`更新设备状态: ${deviceId} -> ${status}`);

    // 查找设备
    let device = null;
    for (const [socketId, deviceData] of devices) {
      if (deviceData.deviceId === deviceId) {
        device = deviceData;
        // 更新内存中的设备状态
        device.status = status;
        device.lastSeen = new Date();
        break;
      }
    }

    if (!device) {
      console.log(`设备未找到，无法更新状态: ${deviceId}`);
      return;
    }

    // 尝试更新数据库状态（如果可用）
    if (pool) {
      try {
        await pool.execute(`
          UPDATE devices SET status = ?, last_seen = NOW()
          WHERE device_id = ?
        `, [status, deviceId]);

        console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
      } catch (dbError) {
        console.error('数据库状态更新失败:', dbError);
      }
    }

    // 通知所有Web客户端设备状态更新
    const statusChange = {
      type: 'device_status_update',
      deviceId: deviceId,
      deviceName: device.deviceName,
      status: status,
      timestamp: new Date()
    };

    io.emit('device_status_update', {
      deviceId: deviceId,
      status: status,
      lastSeen: device.lastSeen
    });

    console.log(`设备状态更新通知已发送: ${deviceId} -> ${status}`);
  }

  // 设备注册API (连接码优先，支持已注册设备重连) - 已添加用户隔离
  app.post('/api/device/register', async (req, res) => {
    const { deviceId, deviceName, deviceInfo, deviceIP, connectionCode } = req.body;

    if (!deviceId || !deviceName) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：设备ID和设备名称'
      });
    }

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    let userId = null;
    let username = null;
    let isReconnection = false;

    try {
      // 首先检查设备是否已经注册过
      console.log(`[设备注册] 检查设备${deviceId}是否已注册`);
      const [existingDevices] = await pool.execute(
        'SELECT device_id, user_id FROM devices WHERE device_id = ? AND user_id IS NOT NULL',
        [deviceId]
      );

      if (existingDevices.length > 0) {
        // 设备已注册，允许重连
        const existingDevice = existingDevices[0];
        userId = existingDevice.user_id;
        isReconnection = true;

        // 获取用户信息
        const [users] = await pool.execute(
          'SELECT username FROM users WHERE id = ?',
          [userId]
        );

        if (users.length > 0) {
          username = users[0].username;
          console.log(`[设备重连] 设备${deviceId}重新连接，保持原有分配: 用户${userId}(${username})`);
        } else {
          console.log(`[设备重连] 警告：设备${deviceId}的用户${userId}不存在，需要重新分配`);
          isReconnection = false;
        }
      }

      // 如果不是重连，则必须提供连接码
      if (!isReconnection) {
        if (!connectionCode) {
          return res.status(400).json({
            success: false,
            message: '新设备首次连接必须提供连接码'
          });
        }

        console.log(`[设备注册] 新设备${deviceId}使用连接码注册: ${connectionCode}`);

        // 验证连接码
        const [codes] = await pool.execute(`
          SELECT id, user_id, username, max_devices, used_count, expires_at, is_active
          FROM device_connection_codes
          WHERE code = ?
        `, [connectionCode]);

        if (codes.length === 0) {
          return res.status(404).json({
            success: false,
            message: '连接码不存在'
          });
        }

        const connectionCodeData = codes[0];

        // 检查连接码是否激活
        if (!connectionCodeData.is_active) {
          return res.status(400).json({
            success: false,
            message: '连接码已禁用'
          });
        }

        // 检查是否过期
        if (connectionCodeData.expires_at && new Date() > new Date(connectionCodeData.expires_at)) {
          return res.status(400).json({
            success: false,
            message: '连接码已过期'
          });
        }

        // 检查使用次数限制
        if (connectionCodeData.used_count >= connectionCodeData.max_devices) {
          return res.status(400).json({
            success: false,
            message: '连接码使用次数已达上限'
          });
        }

        // 检查设备是否已经使用过此连接码
        const [existingConnections] = await pool.execute(
          'SELECT id FROM device_connections WHERE device_id = ? AND connection_code = ?',
          [deviceId, connectionCode]
        );

        if (existingConnections.length === 0) {
          // 记录设备连接
          await pool.execute(`
            INSERT INTO device_connections
            (device_id, connection_code, user_id, device_name, device_info)
            VALUES (?, ?, ?, ?, ?)
          `, [deviceId, connectionCode, connectionCodeData.user_id, deviceName, JSON.stringify(deviceInfo || {})]);

          // 更新连接码使用次数
          await pool.execute(
            'UPDATE device_connection_codes SET used_count = used_count + 1 WHERE code = ?',
            [connectionCode]
          );
        }

        userId = connectionCodeData.user_id;
        username = connectionCodeData.username;
        console.log(`[设备注册] 连接码验证成功，设备${deviceId}分配给用户${userId}(${username})`);
      }

    if (!deviceId || !deviceName) {
      return res.status(400).json({ success: false, message: '设备ID和名称不能为空' });
    }

    console.log(`[设备注册] 用户${userId}注册设备: ${deviceId} (${deviceName})`);

    // 检查设备是否在最近断开列表中（防止立即重连）
    if (recentlyDisconnectedDevices.has(deviceId)) {
      const disconnectTime = recentlyDisconnectedDevices.get(deviceId);
      const timeSinceDisconnect = Date.now() - disconnectTime;
      const cooldownPeriod = 10000; // 10秒冷却期

      if (timeSinceDisconnect < cooldownPeriod) {
        const remainingTime = Math.ceil((cooldownPeriod - timeSinceDisconnect) / 1000);
        console.log(`设备 ${deviceId} 在冷却期内尝试重连，剩余 ${remainingTime} 秒`);
        return res.status(429).json({
          success: false,
          message: `设备刚刚断开连接，请等待 ${remainingTime} 秒后再重新连接`,
          cooldownRemaining: remainingTime
        });
      } else {
        // 冷却期已过，移除记录
        recentlyDisconnectedDevices.delete(deviceId);
        console.log(`设备 ${deviceId} 冷却期已过，允许重新连接`);
      }
    }

    // 检查设备是否已存在，如果存在则保持原有所属权
    if (pool) {
      try {
        const [existingDevices] = await pool.execute(
          'SELECT user_id, device_name FROM devices WHERE device_id = ?',
          [deviceId]
        );

        if (existingDevices.length > 0) {
          const existingUserId = existingDevices[0].user_id;

          // 如果设备已属于其他用户，保持原有所属权，不允许token用户覆盖
          if (existingUserId && existingUserId !== userId) {
            console.log(`[设备注册拒绝] 设备${deviceId}已属于用户${existingUserId}，拒绝用户${userId}的注册请求`);
            // 只更新设备的连接状态和时间，不改变所属权
            await pool.execute(
              'UPDATE devices SET status = ?, last_seen = NOW() WHERE device_id = ?',
              ['online', deviceId]
            );
            console.log(`✅ 设备${deviceId}状态已更新为在线，但保持原有所属权(用户${existingUserId})`);

            // 返回成功响应，但不进行设备重新注册
            return res.json({
              success: true,
              message: '设备连接状态已更新',
              deviceId: deviceId,
              note: `设备已属于用户${existingUserId}，保持原有所属权`
            });
          } else {
            console.log(`[设备注册] 设备${deviceId}重新连接，所属权保持不变(用户${userId})`);
          }
        }
      } catch (dbError) {
        console.error('检查设备所属权失败:', dbError);
      }
    }

    const currentIP = deviceIP || (deviceInfo && deviceInfo.ipAddress) || '未知';

    // 检查设备是否正在执行脚本
    let deviceStatus = 'online';

    // 检查是否有待执行的命令（说明设备正在执行脚本）
    if (pendingCommands.has(deviceId) && pendingCommands.get(deviceId).length > 0) {
      deviceStatus = 'busy';
      console.log(`设备 ${deviceId} 正在执行脚本，保持忙碌状态`);
    }

    try {
      // 尝试保存到数据库（如果可用）- 使用INSERT ON DUPLICATE KEY UPDATE
      if (pool) {
        try {
          // 对于新设备，如果user_id为admin(1)，则表示这是设备池中的设备
          // 检查是否为新设备（不存在于数据库中）
          const [existingDevices] = await pool.execute(
            'SELECT device_id, user_id FROM devices WHERE device_id = ?',
            [deviceId]
          );

          let finalUserId = userId;
          let isNewDevice = existingDevices.length === 0;

          if (isNewDevice) {
            // 新设备：如果连接用户是admin，则保持admin；否则分配给连接用户
            console.log(`[新设备注册] 设备${deviceId}首次连接，连接用户: ${userId}`);
            finalUserId = userId; // 使用连接时的用户ID
          } else {
            // 现有设备：保持原有所属权，除非原来没有所属权
            const existingUserId = existingDevices[0].user_id;
            if (existingUserId && existingUserId !== 0) {
              finalUserId = existingUserId; // 保持原有所属权
              console.log(`[设备重连] 设备${deviceId}保持原有所属权: 用户${existingUserId}`);
            } else {
              finalUserId = userId; // 原来没有所属权，分配给当前用户
              console.log(`[设备分配] 设备${deviceId}原无所属权，分配给用户${userId}`);
            }
          }

          await pool.execute(`
            INSERT INTO devices (device_id, device_name, device_info, status, last_seen, user_id, created_at)
            VALUES (?, ?, ?, ?, NOW(), ?, NOW())
            ON DUPLICATE KEY UPDATE
            device_name = VALUES(device_name),
            device_info = VALUES(device_info),
            status = VALUES(status),
            last_seen = NOW()
            ${isNewDevice ? ', user_id = VALUES(user_id)' : ''}
          `, [
            deviceId,
            deviceName,
            JSON.stringify({
              ...deviceInfo,
              ipAddress: currentIP,
              registrationTime: new Date().toISOString(),
              connectionType: 'http'
            }),
            deviceStatus,
            finalUserId
          ]);

          console.log(`✅ 设备信息已保存到数据库: ${deviceId} (用户${userId})`);

          // 验证数据库状态
          const [rows] = await pool.execute(
            'SELECT status, user_id FROM devices WHERE device_id = ?',
            [deviceId]
          );
          if (rows.length > 0) {
            console.log('📊 设备注册后数据库状态:', { status: rows[0].status, user_id: rows[0].user_id });
          }
        } catch (dbError) {
          console.error('数据库保存失败，继续使用内存模式:', dbError);
        }
      }

      // 检查是否有相同IP的设备已连接，如果有则断开旧连接
      // 注意：只断开群控服务器连接，不影响VSCode调试连接（端口9317）
      if (currentIP && currentIP !== '未知') {
        for (const [socketId, device] of devices) {
          if (device.deviceInfo && device.deviceInfo.ipAddress === currentIP &&
              device.status === 'online' &&
              (socketId.startsWith('http_') || (!socketId.startsWith('vscode_') && !socketId.startsWith('debug_')))) {
            console.log(`检测到同IP设备重连: ${currentIP}, 断开旧连接: ${device.deviceName} (${device.deviceId}), socketId: ${socketId}`);

            // 通知Web客户端旧设备离线
            for (const [clientSocketId] of webClients) {
              const clientSocket = io.sockets.sockets.get(clientSocketId);
              if (clientSocket) {
                clientSocket.emit('device_offline', {
                  deviceId: device.deviceId
                });
              }
            }

            // 如果是WebSocket连接，断开socket
            if (!socketId.startsWith('http_')) {
              const oldSocket = io.sockets.sockets.get(socketId);
              if (oldSocket) {
                oldSocket.disconnect(true);
              }
            }

            // 移除旧设备连接
            devices.delete(socketId);
            console.log(`已移除旧设备连接: ${socketId}`);
            break;
          }
        }
      }

      // 检查数据库中是否有正在执行的任务（只检查最近的任务）
      if (pool) {
        try {
          // 同时检查小红书和闲鱼执行日志
          const [xiaohongshuRows] = await pool.execute(`
            SELECT execution_status, started_at FROM xiaohongshu_execution_logs
            WHERE device_id = ? AND execution_status IN ('running', 'pending')
            ORDER BY started_at DESC LIMIT 1
          `, [deviceId]);

          const [xianyuRows] = await pool.execute(`
            SELECT execution_status, started_at FROM xianyu_execution_logs
            WHERE device_id = ? AND execution_status IN ('running', 'pending')
            ORDER BY started_at DESC LIMIT 1
          `, [deviceId]);

          // 合并检查结果
          const allRunningTasks = [...xiaohongshuRows, ...xianyuRows];

          if (allRunningTasks.length > 0) {
            // 找到最近的任务
            const latestTask = allRunningTasks.reduce((latest, current) => {
              const currentTime = new Date(current.started_at);
              const latestTime = new Date(latest.started_at);
              return currentTime > latestTime ? current : latest;
            });

            const taskStartTime = new Date(latestTask.started_at);
            const now = new Date();
            const timeDiff = now - taskStartTime;

            // 只考虑最近10分钟内的任务，避免服务器重启后的历史任务影响
            if (timeDiff < 10 * 60 * 1000) { // 10分钟
              deviceStatus = 'busy';
              console.log(`设备 ${deviceId} 在数据库中有最近10分钟内的正在执行任务，保持忙碌状态`);
              console.log(`任务开始时间: ${taskStartTime.toISOString()}, 时间差: ${Math.round(timeDiff/1000)}秒`);
            } else {
              console.log(`设备 ${deviceId} 的数据库任务已过期，忽略历史任务状态`);
              console.log(`任务开始时间: ${taskStartTime.toISOString()}, 时间差: ${Math.round(timeDiff/1000)}秒`);

              // 自动清理过期的任务状态
              try {
                await pool.execute(`
                  UPDATE xiaohongshu_execution_logs
                  SET execution_status = 'stopped',
                      progress_percentage = 0,
                      status_message = '已过期自动停止'
                  WHERE device_id = ? AND execution_status IN ('running', 'pending')
                  AND started_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                `, [deviceId]);

                await pool.execute(`
                  UPDATE xianyu_execution_logs
                  SET execution_status = 'stopped',
                      progress_percentage = 0,
                      status_message = '已过期自动停止'
                  WHERE device_id = ? AND execution_status IN ('running', 'pending')
                  AND started_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                `, [deviceId]);

                console.log(`🧹 已自动清理设备 ${deviceId} 的过期任务状态`);
              } catch (cleanupError) {
                console.error('自动清理过期任务失败:', cleanupError);
              }
            }
          } else {
            console.log(`设备 ${deviceId} 在数据库中没有正在执行的任务`);
          }
        } catch (dbError) {
          console.error('检查数据库任务状态失败:', dbError);
        }
      }

      // 注册设备到内存
      const deviceData = {
        socketId: 'http_' + Date.now(),
        deviceId,
        deviceName,
        deviceInfo: {
          ...deviceInfo,
          ipAddress: currentIP
        },
        status: deviceStatus,
        connectedAt: new Date(),
        lastSeen: new Date()
      };

      devices.set(deviceData.socketId, deviceData);

      console.log(`HTTP设备已注册: ${deviceName} (${deviceId}) IP: ${currentIP}`);
      console.log(`设备注册详情: socketId=${deviceData.socketId}, deviceId=${deviceData.deviceId}, deviceName=${deviceData.deviceName}`);

      // 打印详细设备信息
      if (deviceInfo) {
        console.log(`设备详细信息:`);
        console.log(`- 屏幕分辨率: ${deviceInfo.screenWidth || '未知'}x${deviceInfo.screenHeight || '未知'}`);
        console.log(`- 设备品牌: ${deviceInfo.brand || '未知'}`);
        console.log(`- 设备型号: ${deviceInfo.model || '未知'}`);
        console.log(`- Android版本: ${deviceInfo.androidVersion || '未知'}`);
        console.log(`- SDK版本: ${deviceInfo.sdkVersion || '未知'}`);
        console.log(`- 总内存: ${deviceInfo.totalMemory || '未知'}`);
        console.log(`- 可用内存: ${deviceInfo.availableMemory || '未知'}`);
        console.log(`- 电池电量: ${deviceInfo.batteryLevel || '未知'}`);
        console.log(`- 存储信息: ${deviceInfo.storageInfo || '未知'}`);
        console.log(`- Auto.js版本: ${deviceInfo.autoJsVersion || '未知'}`);
        console.log(`- 连接时间: ${deviceInfo.deviceTime || '未知'}`);
      }

      // 通知所有Web客户端有新设备连接
      const statusChange = {
        type: 'device_connected',
        deviceId: deviceId,
        deviceName: deviceName,
        deviceIP: deviceIP,
        deviceInfo: deviceInfo,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);

      res.json({
        success: true,
        message: '设备注册成功',
        data: { deviceId, deviceIP }
      });

    } catch (innerError) {
      console.error('设备注册内部处理失败:', innerError);
      res.status(500).json({
        success: false,
        message: '设备注册内部处理失败: ' + innerError.message
      });
    }

    } catch (error) {
      console.error('设备注册失败:', error);
      res.status(500).json({
        success: false,
        message: '设备注册失败: ' + error.message
      });
    }
  });

  // HTTP设备获取待执行命令的API (原始文件第1200行) - 已移动到server-device-commands.js
  /*
  app.get('/api/device/:deviceId/commands', async (req, res) => {
    const { deviceId } = req.params;

    // 验证设备是否存在
    if (pool) {
      try {
        const [existingDevices] = await pool.execute(
          'SELECT user_id, device_name FROM devices WHERE device_id = ?',
          [deviceId]
        );

        if (existingDevices.length === 0) {
          console.log(`[设备命令] 设备${deviceId}未注册`);
          return res.status(404).json({
            success: false,
            message: '设备未注册，请先注册设备'
          });
        }
      } catch (dbError) {
        console.error('验证设备存在性失败:', dbError);
        return res.status(500).json({
          success: false,
          message: '数据库查询失败'
        });
      }
    }

    // 查找设备记录（通过deviceId查找，而不是socketId）
    let device = null;
    let deviceSocketId = null;

    for (const [socketId, deviceData] of devices) {
      if (deviceData.deviceId === deviceId) {
        device = deviceData;
        deviceSocketId = socketId;
        break;
      }
    }

    if (!device) {
      // 检查是否有待执行的命令（包括断开命令和视频传输命令）
      let hasCommands = false;
      let commandToReturn = null;
      let commandSource = '';

      // 优先检查 pendingCommands
      if (pendingCommands.has(deviceId)) {
        const commands = pendingCommands.get(deviceId);
        if (commands.length > 0) {
          commandToReturn = commands.shift();
          commandSource = 'pendingCommands';
          hasCommands = true;

          if (commands.length === 0) {
            pendingCommands.delete(deviceId);
          }
        }
      }

      // 如果没有 pendingCommands，检查 deviceCommands
      if (!hasCommands && deviceCommands[deviceId] && deviceCommands[deviceId].length > 0) {
        const commands = deviceCommands[deviceId];
        commandToReturn = commands.shift();
        commandSource = 'deviceCommands';
        hasCommands = true;

        if (commands.length === 0) {
          delete deviceCommands[deviceId];
        }
      }

      if (hasCommands) {
        console.log(`[设备命令] 设备 ${deviceId} 未注册，但有待执行命令(${commandSource})，允许用户${userId}获取命令`);

        // 创建临时设备记录以支持命令获取
        const tempDevice = {
          deviceId: deviceId,
          lastPollTime: Date.now(),
          status: 'online',
          isTemporary: true,  // 标记为临时记录
          userId: userId     // 添加用户ID标识
        };

        // 查找合适的socketId（优先使用http_格式）
        let tempSocketId = `temp_${deviceId}_${Date.now()}`;
        devices.set(tempSocketId, tempDevice);

        console.log(`[设备命令] 为设备 ${deviceId} 创建临时记录，socketId: ${tempSocketId} (用户${userId})`);
        console.log(`[设备命令] 返回${commandSource}命令给临时设备 ${deviceId}:`, JSON.stringify(commandToReturn));

        return res.json({
          success: true,
          data: commandToReturn
        });
      }

      console.log(`⚠️ 设备 ${deviceId} 未注册且无待执行命令，拒绝请求`);
      return res.status(404).json({
        success: false,
        message: '设备未注册，请先注册设备'
      });
    }

    // 更新设备的最后轮询时间和活动时间
    device.lastPollTime = Date.now();
    device.lastSeen = new Date();

    // 如果设备状态是离线但正在轮询，说明设备实际是在线的，需要更新状态
    if (device.status === 'offline') {
      console.log(`📱 设备 ${deviceId} 正在轮询但状态为离线，更新为在线状态`);
      device.status = 'online';

      // 更新数据库状态
      if (pool) {
        try {
          await pool.execute(`
            UPDATE devices SET status = 'online', last_seen = NOW()
            WHERE device_id = ?
          `, [deviceId]);
          console.log(`数据库中设备状态已更新为在线: ${deviceId}`);
        } catch (dbError) {
          console.error('轮询时更新数据库状态失败:', dbError);
        }
      }

      // 通知前端设备状态更新
      io.emit('device_status_update', {
        deviceId: deviceId,
        status: 'online',
        lastSeen: new Date()
      });

      console.log(`📡 已通知前端设备 ${deviceId} 状态恢复在线`);
    }

    // 使用节流日志，避免频繁输出设备命令请求信息
    const deviceLogKey = `device_command_${deviceId}`;
    throttledLog(deviceLogKey, `设备 ${deviceId} 请求获取命令，当前状态: ${device.status}`);
    throttledLog(`${deviceLogKey}_queue`, `当前待执行命令队列状态: pendingCommands=${pendingCommands.has(deviceId)}, deviceCommands=${deviceCommands[deviceId] ? true : false}`);

    // 优先检查 pendingCommands（脚本执行命令）
    if (pendingCommands.has(deviceId)) {
      const commands = pendingCommands.get(deviceId);
      // 使用节流日志，避免频繁输出队列长度信息
      throttledLog(`pending_length_${deviceId}`, `- 设备 ${deviceId} pendingCommands队列长度: ${commands.length}`);

      if (commands.length > 0) {
        // 返回第一个命令并从队列中移除
        const command = commands.shift();

        console.log(`- 返回pendingCommands命令给设备 ${deviceId}:`, JSON.stringify(command));
        console.log(`- 设备 ${deviceId} 剩余pendingCommands数量:`, commands.length);

        // 如果队列为空，删除该设备的队列
        if (commands.length === 0) {
          pendingCommands.delete(deviceId);
          console.log(`- 设备 ${deviceId} pendingCommands队列已清空`);
        }

        return res.json({
          success: true,
          data: command
        });
      } else {
        // 使用节流日志，避免频繁输出队列为空信息
        throttledLog(`pending_empty_${deviceId}`, `- 设备 ${deviceId} pendingCommands队列为空`);
      }
    } else {
      // 使用节流日志，避免频繁输出没有队列信息
      throttledLog(`no_pending_${deviceId}`, `- 设备 ${deviceId} 没有pendingCommands队列`);
    }

    // 检查 deviceCommands（视频传输等其他命令）
    if (deviceCommands[deviceId] && deviceCommands[deviceId].length > 0) {
      const commands = deviceCommands[deviceId];
      const command = commands.shift();

      console.log(`- 返回deviceCommands命令给设备 ${deviceId}:`, JSON.stringify(command));
      console.log(`- 设备 ${deviceId} 剩余deviceCommands数量:`, commands.length);

      // 如果队列为空，删除该设备的队列
      if (commands.length === 0) {
        delete deviceCommands[deviceId];
        console.log(`- 设备 ${deviceId} deviceCommands队列已清空`);
      }

      return res.json({
        success: true,
        data: command
      });
    } else {
      // 使用节流日志，避免频繁输出没有队列或队列为空信息
      throttledLog(`no_device_commands_${deviceId}`, `- 设备 ${deviceId} 没有deviceCommands队列或队列为空`);
    }

    // 没有待执行命令
    // 使用节流日志，避免频繁输出无命令信息
    throttledLog(`no_command_${deviceId}`, `- 设备 ${deviceId} 无待执行命令`);
    res.json({
      success: true,
      data: null
    });
  });
  */

  // HTTP设备上报脚本执行结果API (原始文件第1421行) - 已移动到server-device-commands.js
  /*
  app.post('/api/device/:deviceId/result', async (req, res) => {
    const { deviceId } = req.params;
    const { logId, result, status } = req.body;

    // 验证设备是否存在
    if (pool) {
      try {
        const [existingDevices] = await pool.execute(
          'SELECT user_id, device_name FROM devices WHERE device_id = ?',
          [deviceId]
        );

        if (existingDevices.length === 0) {
          console.log(`[脚本结果] 设备${deviceId}未注册`);
          return res.status(404).json({
            success: false,
            message: '设备未注册'
          });
        }
      } catch (dbError) {
        console.error('验证设备存在性失败:', dbError);
        return res.status(500).json({
          success: false,
          message: '数据库查询失败'
        });
      }
    }

    // 更新日志
    const log = logs.find(l => l.id === logId);
    if (log) {
      log.result = result;
      log.status = status;
      log.completed_at = new Date();
    }

    // 检查是否是小红书任务的执行结果
    if (xiaohongshuLogService && logId && logId.includes('xiaohongshu_')) {
      try {
        console.log(`[状态更新] 收到设备结果: logId=${logId}, status=${status}, result=${result}`);

        // 处理停止命令的logId（去掉stop_前缀）
        let actualLogId = logId;
        if (logId.startsWith('stop_')) {
          actualLogId = logId.substring(5); // 去掉"stop_"前缀
          console.log(`[状态更新] 检测到停止命令，原始logId: ${actualLogId}`);

          // 检查是否有重复的设备ID，如果有则去掉重复部分
          // 例如：xiaohongshu_profile_1751366484142_device_192_168_1_71_device_192_168_1_71
          // 应该变为：xiaohongshu_profile_1751366484142_device_192_168_1_71
          const parts = actualLogId.split('_');
          if (parts.length >= 6) {
            // 检查最后两个部分是否重复（device_xxx_device_xxx）
            const lastPart = parts[parts.length - 1];
            const secondLastPart = parts[parts.length - 2];
            const thirdLastPart = parts[parts.length - 3];
            const fourthLastPart = parts[parts.length - 4];

            if (fourthLastPart === 'device' && secondLastPart === 'device' && thirdLastPart === lastPart) {
              // 去掉重复的设备ID部分
              actualLogId = parts.slice(0, -2).join('_');
              console.log(`[状态更新] 检测到重复设备ID，修正后的logId: ${actualLogId}`);
            }
          }
        }

        // 更新小红书执行日志
        // 判断执行状态：停止、成功、失败
        let finalStatus, progress;

        if (status === 'stopped') {
          // 脚本被停止
          finalStatus = 'stopped';
          progress = 0;
          console.log(`[状态更新] 检测到停止状态: ${actualLogId}`);
        } else {
          // 判断执行是否成功：status为'success'或者result中包含成功信息
          const isSuccess = status === 'success' ||
                           (result && (
                             result.includes('执行完成') ||
                             result.includes('成功') ||
                             result.includes('完成')
                           )) ||
                           status === 'completed';

          finalStatus = isSuccess ? 'completed' : 'failed';
          progress = isSuccess ? 100 : 0;
          console.log(`[状态更新] 检测到${isSuccess ? '成功' : '失败'}状态: ${actualLogId}`);
        }

        // 根据最终状态设置状态消息
        let statusMessage, executionLogs;
        if (finalStatus === 'stopped') {
          statusMessage = '已停止';
          executionLogs = `脚本已被停止: ${result || '用户手动停止'}`;
        } else if (finalStatus === 'completed') {
          statusMessage = '执行完成';
          executionLogs = `脚本执行成功: ${result || '无详细信息'}`;
        } else {
          statusMessage = '执行失败';
          executionLogs = `脚本执行失败: ${result || '无详细信息'}`;
        }

        console.log(`[状态更新] 准备更新数据库: actualLogId=${actualLogId}, finalStatus=${finalStatus}, progress=${progress}, statusMessage=${statusMessage}`);

        await xiaohongshuLogService.updateExecutionStatus(
          actualLogId,
          finalStatus,
          progress,
          statusMessage,
          executionLogs
        );

        console.log(`[状态更新] 数据库更新完成: ${actualLogId} -> ${finalStatus} (${progress}%)`);
        console.log(`[状态更新] 状态消息: ${statusMessage}`);

        if (result) {
          await xiaohongshuLogService.updateExecutionResult(actualLogId, {
            status: status,
            result: result,
            timestamp: new Date().toISOString()
          });
        }

        console.log(`小红书执行状态已更新: ${actualLogId} -> ${finalStatus}`);
        console.log(`小红书执行日志已更新: ${actualLogId} -> ${finalStatus} (${progress}%)`);

        // 恢复设备状态为在线
        try {
          await updateDeviceStatus(deviceId, 'online');
          console.log(`HTTP设备脚本执行完成，设备状态已恢复为在线: ${deviceId}`);
        } catch (error) {
          console.error('恢复HTTP设备状态失败:', error);
        }

        // 通知前端重置脚本执行状态
        const completionEvent = {
          deviceId: deviceId,
          taskId: actualLogId,
          status: finalStatus === 'completed' ? 'success' :
                  finalStatus === 'stopped' ? 'stopped' : 'failed',
          message: result,
          timestamp: new Date().toISOString()
        };

        console.log(`📡 发送脚本执行完成事件: ${JSON.stringify(completionEvent)}`);
        io.emit('xiaohongshu_execution_completed', completionEvent);

        // 也发送给所有Web客户端
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket && clientSocket.connected) {
            clientSocket.emit('xiaohongshu_execution_completed', completionEvent);
            console.log(`📡 已发送脚本完成事件到客户端: ${clientSocketId}`);
          }
        }

        // 通知手机端关闭小红书应用
        console.log(`通知设备 ${deviceId} 关闭小红书应用`);
        io.to(deviceId).emit('script_command', {
          type: 'close_xiaohongshu_app',
          deviceId: deviceId,
          reason: finalStatus === 'completed' ? '脚本执行完成' :
                  finalStatus === 'stopped' ? '脚本被停止' : '脚本执行失败',
          timestamp: new Date().toISOString()
        });
      } catch (logError) {
        console.error('更新小红书执行日志失败:', logError);
      }
    }

    // 检查是否是闲鱼任务的执行结果
    if (xianyuLogService && logId && logId.includes('xianyu_')) {
      try {
        console.log(`[闲鱼状态更新] 收到设备结果: logId=${logId}, status=${status}, result=${result}`);

        // 处理停止命令的logId（去掉stop_前缀）
        let actualLogId = logId;
        if (logId.startsWith('stop_')) {
          actualLogId = logId.substring(5); // 去掉"stop_"前缀
          console.log(`[闲鱼状态更新] 检测到停止命令，原始logId: ${actualLogId}`);
        }

        // 更新闲鱼执行日志
        // 判断执行状态：停止、成功、失败
        let finalStatus, progress;

        if (status === 'stopped') {
          // 脚本被停止
          finalStatus = 'stopped';
          progress = 0;
          console.log(`[闲鱼状态更新] 检测到停止状态: ${actualLogId}`);
        } else {
          // 判断执行是否成功：status为'success'或者result中包含成功信息
          const isSuccess = status === 'success' ||
                           (result && (
                             result.includes('执行完成') ||
                             result.includes('成功') ||
                             result.includes('完成')
                           )) ||
                           status === 'completed';

          finalStatus = isSuccess ? 'completed' : 'failed';
          progress = isSuccess ? 100 : 0;
          console.log(`[闲鱼状态更新] 检测到${isSuccess ? '成功' : '失败'}状态: ${actualLogId}`);
        }

        // 根据最终状态设置状态消息
        let statusMessage, executionLogs;
        if (finalStatus === 'stopped') {
          statusMessage = '已停止';
          executionLogs = `脚本已被停止: ${result || '用户手动停止'}`;
        } else if (finalStatus === 'completed') {
          statusMessage = '执行完成';
          executionLogs = `脚本执行成功: ${result || '无详细信息'}`;
        } else {
          statusMessage = '执行失败';
          executionLogs = `脚本执行失败: ${result || '无详细信息'}`;
        }

        console.log(`[闲鱼状态更新] 准备更新数据库: actualLogId=${actualLogId}, finalStatus=${finalStatus}, progress=${progress}, statusMessage=${statusMessage}`);

        await xianyuLogService.updateExecutionStatus(
          actualLogId,
          finalStatus,
          progress,
          statusMessage,
          executionLogs
        );

        console.log(`[闲鱼状态更新] 数据库更新完成: ${actualLogId} -> ${finalStatus} (${progress}%)`);
        console.log(`[闲鱼状态更新] 状态消息: ${statusMessage}`);

        if (result) {
          await xianyuLogService.updateExecutionResult(actualLogId, {
            status: status,
            result: result,
            timestamp: new Date().toISOString()
          });
        }

        console.log(`闲鱼执行状态已更新: ${actualLogId} -> ${finalStatus}`);
        console.log(`闲鱼执行日志已更新: ${actualLogId} -> ${finalStatus} (${progress}%)`);

        // 恢复设备状态为在线
        try {
          await updateDeviceStatus(deviceId, 'online');
          console.log(`HTTP设备闲鱼脚本执行完成，设备状态已恢复为在线: ${deviceId}`);
        } catch (error) {
          console.error('恢复HTTP设备状态失败:', error);
        }

        // 通知前端重置脚本执行状态
        const completionEvent = {
          deviceId: deviceId,
          taskId: actualLogId,
          status: finalStatus === 'completed' ? 'success' :
                  finalStatus === 'stopped' ? 'stopped' : 'failed',
          message: result,
          timestamp: new Date().toISOString()
        };

        console.log(`📡 发送闲鱼脚本执行完成事件: ${JSON.stringify(completionEvent)}`);
        io.emit('xianyu_execution_completed', completionEvent);

        // 也发送给所有Web客户端
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket && clientSocket.connected) {
            clientSocket.emit('xianyu_execution_completed', completionEvent);
            console.log(`📡 已发送闲鱼脚本完成事件到客户端: ${clientSocketId}`);
          }
        }

      } catch (logError) {
        console.error('更新闲鱼执行日志失败:', logError);
      }
    }

    console.log(`HTTP设备执行结果: ${deviceId}, 状态: ${status}`);

    res.json({
      success: true,
      message: '结果已记录'
    });
  });
  */

  // 断开设备连接API (PC端断开设备，不删除设备记录) (原始文件第1577行) - 已添加用户隔离
  app.delete('/api/device/:id', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { id } = req.params;
    const userId = req.currentUserId;

    console.log(`[设备断开] 用户${userId}请求断开设备: ${id}`);

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(id, userId);
    if (!hasPermission) {
      console.log(`[设备断开] 用户${userId}无权断开设备${id}`);
      return res.status(403).json({
        success: false,
        message: '无权操作此设备'
      });
    }

    // 查找设备
    let deviceToDisconnect = null;
    let socketIdToDisconnect = null;

    for (const [socketId, device] of devices) {
      if (device.deviceId === id) {
        deviceToDisconnect = device;
        socketIdToDisconnect = socketId;
        break;
      }
    }

    if (!deviceToDisconnect) {
      console.log(`设备不在内存中: ${id}`);
      console.log(`当前连接的设备:`, Array.from(devices.keys()));

      // 检查数据库中是否存在该设备（带用户过滤）
      try {
        const [rows] = await dbEnhancer.executeWithUserFilter(
          'SELECT * FROM devices WHERE device_id = ?',
          [id],
          userId
        );

        if (rows.length === 0) {
          return res.status(404).json({ success: false, message: '设备不存在或无权访问' });
        }

        // 设备存在于数据库但不在内存中，说明已经离线
        // 直接更新数据库状态为离线（带用户过滤）
        await dbEnhancer.executeWithUserFilter(`
          UPDATE devices SET status = 'offline', last_seen = NOW()
          WHERE device_id = ?
        `, [id], userId);

        console.log(`[设备断开] 设备已离线，数据库状态已更新: ${id} (用户${userId})`);

        // 通知所有Web客户端设备状态变化
        const statusChange = {
          type: 'device_disconnected',
          deviceId: id,
          deviceName: rows[0].device_name,
          userId: userId, // 添加用户ID用于前端过滤
          timestamp: new Date()
        };

        io.emit('device_status_changed', statusChange);

        return res.json({
          success: true,
          message: '设备已离线，状态已更新'
        });

      } catch (dbError) {
        console.error('数据库查询失败:', dbError);
        return res.status(500).json({ success: false, message: '数据库查询失败' });
      }
    }

    try {
      // 更新数据库中设备状态为离线（不删除记录）
      if (pool) {
        try {
          const [result] = await pool.execute(`
            UPDATE devices SET status = 'offline', last_seen = NOW()
            WHERE device_id = ?
          `, [id]);

          if (result.affectedRows > 0) {
            console.log('设备状态已更新为离线:', id);
          }
        } catch (dbError) {
          console.error('数据库状态更新失败:', dbError);
        }
      }

      // 添加到最近断开列表
      recentlyDisconnectedDevices.set(id, Date.now());

      // 先通知手机端设备控制器断开连接
      if (!socketIdToDisconnect.startsWith('http_')) {
        const socket = io.sockets.sockets.get(socketIdToDisconnect);
        if (socket) {
          console.log(`通知手机端设备控制器断开连接: ${socketIdToDisconnect}`);

          // 发送断开通知给手机端
          socket.emit('server_disconnect_request', {
            reason: 'manual_disconnect',
            message: 'PC端请求断开连接',
            timestamp: new Date().toISOString()
          });

          // 等待一段时间让手机端处理断开通知，然后强制断开
          setTimeout(() => {
            if (socket.connected) {
              console.log(`强制断开WebSocket连接: ${socketIdToDisconnect}`);
              socket.disconnect(true);
            }
          }, 2000); // 等待2秒
        }
      } else {
        // HTTP设备：添加断开命令到队列
        if (!pendingCommands.has(id)) {
          pendingCommands.set(id, []);
        }
        pendingCommands.get(id).push({
          type: 'disconnect',
          reason: 'manual_disconnect',
          message: 'PC端请求断开连接',
          timestamp: Date.now()
        });
        console.log(`已添加断开命令到HTTP设备队列: ${id}`);
      }

      // 从内存中移除设备
      devices.delete(socketIdToDisconnect);
      console.log(`设备已从内存中移除: ${id} (${socketIdToDisconnect})`);

      // 通知所有Web客户端设备状态变化
      const statusChange = {
        type: 'device_disconnected',
        deviceId: id,
        deviceName: deviceToDisconnect.deviceName,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);

      console.log('WebSocket通知已发送');

      res.json({
        success: true,
        message: '设备已断开连接'
      });
    } catch (error) {
      console.error('断开设备连接失败:', error);
      res.status(500).json({
        success: false,
        message: '断开设备连接失败: ' + error.message
      });
    }
  });

  // 删除设备记录API (真正删除设备记录) (原始文件第1748行) - 已添加用户隔离
  app.delete('/api/device/:id/delete', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { id } = req.params;
    const userId = req.currentUserId;

    console.log(`[设备删除] 用户${userId}请求删除设备记录: ${id}`);

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(id, userId);
    if (!hasPermission) {
      console.log(`[设备删除] 用户${userId}无权删除设备${id}`);
      return res.status(403).json({
        success: false,
        message: '无权删除此设备'
      });
    }

    try {
      // 查找设备
      let deviceToDelete = null;
      let socketIdToDelete = null;

      for (const [socketId, device] of devices) {
        if (device.deviceId === id) {
          deviceToDelete = device;
          socketIdToDelete = socketId;
          break;
        }
      }

      // 从数据库中删除设备记录（带用户过滤）
      if (pool) {
        try {
          const [result] = await dbEnhancer.executeWithUserFilter(`
            DELETE FROM devices WHERE device_id = ?
          `, [id], userId);

          if (result.affectedRows > 0) {
            console.log(`[设备删除] 设备记录已从数据库删除: ${id} (用户${userId})`);
          } else {
            console.log('数据库中未找到设备记录:', id);
          }
        } catch (dbError) {
          console.error('数据库删除失败:', dbError);
          return res.status(500).json({
            success: false,
            message: '删除设备记录失败: ' + dbError.message
          });
        }
      }

      // 如果设备当前在线，断开连接
      if (deviceToDelete && socketIdToDelete) {
        console.log('断开并删除在线设备:', id);

        // 清理该设备相关的小红书任务
        if (xiaohongshuLogService) {
          try {
            // 停止该设备的所有小红书任务
            console.log(`清理设备 ${id} 的小红书任务`);
            // 这里可以添加具体的清理逻辑
          } catch (cleanupError) {
            console.error('清理小红书任务失败:', cleanupError);
          }
        }

        // 清理该设备相关的闲鱼任务
        if (xianyuLogService) {
          try {
            // 停止该设备的所有闲鱼任务
            console.log(`清理设备 ${id} 的闲鱼任务`);
            // 这里可以添加具体的清理逻辑
          } catch (cleanupError) {
            console.error('清理闲鱼任务失败:', cleanupError);
          }
        }

        // 断开WebSocket连接
        const socket = io.sockets.sockets.get(socketIdToDelete);
        if (socket) {
          socket.disconnect(true);
        }

        // 从内存中移除
        devices.delete(socketIdToDelete);

        // 清理待执行命令
        if (pendingCommands.has(id)) {
          pendingCommands.delete(id);
        }
      }

      console.log(`设备记录删除成功: ${id}`);

      // 通知所有Web客户端设备已删除
      const statusChange = {
        type: 'device_deleted',
        deviceId: id,
        deviceName: deviceToDelete ? deviceToDelete.deviceName : id,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);

      res.json({
        success: true,
        message: '设备记录删除成功'
      });

    } catch (error) {
      console.error('删除设备记录失败:', error);
      res.status(500).json({
        success: false,
        message: '删除设备记录失败: ' + error.message
      });
    }
  });

  // 设备检查停止信号API (原始文件第4507行)
  app.get('/api/device/check-stop', (req, res) => {
    try {
      const { deviceId } = req.query;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID'
        });
      }

      const stopSignal = deviceStopSignals.get(deviceId);

      res.json({
        success: true,
        shouldStop: stopSignal ? stopSignal.shouldStop : false,
        timestamp: stopSignal ? stopSignal.timestamp : null
      });

    } catch (error) {
      console.error('检查停止信号失败:', error);
      res.status(500).json({
        success: false,
        message: '检查失败: ' + error.message
      });
    }
  });

  // 设备通知脚本已停止API (原始文件第4536行)
  app.post('/api/device/script-stopped', (req, res) => {
    try {
      const { deviceId, timestamp } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID'
        });
      }

      console.log(`设备 ${deviceId} 报告脚本已停止`);

      // 清除停止信号
      deviceStopSignals.delete(deviceId);

      // 通知Web客户端设备脚本已停止
      io.emit('device_script_stopped', {
        deviceId: deviceId,
        timestamp: timestamp || new Date()
      });

      res.json({
        success: true,
        message: '已确认脚本停止'
      });

    } catch (error) {
      console.error('处理脚本停止通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理失败: ' + error.message
      });
    }
  });

  // HTTP设备主动断开连接API (原始文件第10530行) - 已移动到server-device-commands.js
  /*
  app.post('/api/device/:deviceId/disconnect', async (req, res) => {
    const { deviceId } = req.params;

    console.log(`收到设备主动断开请求: ${deviceId}`);

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '无权操作此设备'
      });
    }

    // 尝试更新数据库状态（如果可用）
    if (pool) {
      try {
        await pool.execute(`
          UPDATE devices SET status = 'offline', last_seen = NOW()
          WHERE device_id = ? AND user_id = ?
        `, [deviceId, userId]);

        console.log('✅ 数据库状态已更新为离线:', deviceId);

        // 验证数据库更新是否成功
        const [rows] = await pool.execute('SELECT status FROM devices WHERE device_id = ? AND user_id = ?', [deviceId, userId]);
        if (rows.length > 0) {
          console.log('📊 数据库中当前状态:', rows[0].status);
        }
      } catch (dbError) {
        console.error('更新设备离线状态失败:', dbError);
      }
    }

    // 查找并移除设备
    let deviceToRemove = null;
    let socketIdToRemove = null;

    console.log(`查找设备进行断开: ${deviceId}`);
    console.log(`当前内存中的设备数量: ${devices.size}`);

    for (const [socketId, device] of devices) {
      console.log(`检查设备: socketId=${socketId}, deviceId=${device.deviceId}, deviceName=${device.deviceName}`);
      if (device.deviceId === deviceId) {
        deviceToRemove = device;
        socketIdToRemove = socketId;
        console.log(`找到匹配的设备: ${device.deviceName} (${device.deviceId})`);
        break;
      }
    }

    if (!deviceToRemove) {
      console.log(`⚠️ 未找到设备 ${deviceId}，可能已经断开或不存在`);
    }

    if (deviceToRemove) {
      // 清理该设备相关的小红书任务
      if (xiaohongshuLogService) {
        try {
          console.log(`清理设备 ${deviceId} 的小红书任务`);
          // 这里可以添加具体的清理逻辑
        } catch (cleanupError) {
          console.error('清理小红书任务失败:', cleanupError);
        }
      }

      // 清理该设备相关的闲鱼任务
      if (xianyuLogService) {
        try {
          console.log(`清理设备 ${deviceId} 的闲鱼任务`);
          // 这里可以添加具体的清理逻辑
        } catch (cleanupError) {
          console.error('清理闲鱼任务失败:', cleanupError);
        }
      }

      // 移除设备
      console.log(`准备删除设备: socketId=${socketIdToRemove}, deviceId=${deviceId}`);
      console.log(`删除前设备数量: ${devices.size}`);

      devices.delete(socketIdToRemove);

      console.log(`删除后设备数量: ${devices.size}`);
      console.log(`验证设备是否已删除: ${devices.has(socketIdToRemove) ? '仍存在' : '已删除'}`);

      // 清理待执行命令
      if (pendingCommands.has(deviceId)) {
        pendingCommands.delete(deviceId);
      }

      console.log(`HTTP设备主动断开: ${deviceToRemove.deviceName} (${deviceId})`);
      console.log(`当前剩余设备数量: ${devices.size}`);

      // 列出剩余的设备
      console.log(`剩余设备列表:`);
      for (const [socketId, device] of devices) {
        console.log(`- ${device.deviceName} (${device.deviceId}) - ${socketId}`);
      }

      // 通知所有Web客户端设备状态变化
      const statusChange = {
        type: 'device_disconnected',
        deviceId: deviceId,
        deviceName: deviceToRemove.deviceName,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);

      console.log('WebSocket通知已发送');

      res.json({
        success: true,
        message: '设备已断开连接'
      });
    } else {
      res.status(404).json({
        success: false,
        message: '设备不存在'
      });
    }
  });
  */

  // 设备应用信息上报API (原始文件第10643行) - 连接码模式，无需token
  app.post('/api/device/:deviceId/apps', async (req, res) => {
    const { deviceId } = req.params;
    const { apps, detectedAt } = req.body;

    console.log(`[设备应用] 设备${deviceId}上报应用信息`);

    // 从数据库获取设备所属的用户ID
    let actualOwnerId = null;
    if (pool) {
      try {
        const [existingDevices] = await pool.execute(
          'SELECT user_id, device_name FROM devices WHERE device_id = ?',
          [deviceId]
        );

        if (existingDevices.length > 0) {
          actualOwnerId = existingDevices[0].user_id;
          console.log(`[设备应用] 设备${deviceId}属于用户${actualOwnerId}`);

          // 更新设备的最后活跃时间
          await pool.execute(
            'UPDATE devices SET last_seen = NOW() WHERE device_id = ?',
            [deviceId]
          );
        } else {
          console.log(`[设备应用] 设备${deviceId}未找到，可能未注册`);
          return res.status(404).json({
            success: false,
            message: '设备未注册'
          });
        }
      } catch (dbError) {
        console.error('检查设备所属权失败:', dbError);
        return res.status(500).json({
          success: false,
          message: '数据库查询失败'
        });
      }
    }

    console.log('请求体:', JSON.stringify(req.body, null, 2));
    console.log('apps类型:', typeof apps);
    console.log('apps内容:', apps);

    if (!apps) {
      return res.status(400).json({
        success: false,
        message: '缺少应用信息'
      });
    }

    if (typeof apps !== 'object') {
      return res.status(400).json({
        success: false,
        message: '应用信息格式错误：apps必须是对象'
      });
    }

    // 确保数组存在，并提供默认值
    const xiaohongshuApps = Array.isArray(apps.xiaohongshu) ? apps.xiaohongshu : [];
    const xianyuApps = Array.isArray(apps.xianyu) ? apps.xianyu : [];

    console.log(`小红书应用数量: ${xiaohongshuApps.length}`);
    console.log(`闲鱼应用数量: ${xianyuApps.length}`);

    // 检查是否至少有一个应用类型包含数据
    if (xiaohongshuApps.length === 0 && xianyuApps.length === 0) {
      console.log('警告：未检测到任何应用，但仍然保存记录');
    }

    try {
      if (pool) {
        // 先清除该设备的旧应用记录（使用实际设备所有者ID）
        await dbEnhancer.executeWithUserFilter(
          'DELETE FROM device_apps WHERE device_id = ?',
          [deviceId],
          actualOwnerId
        );

        // 插入小红书应用信息（使用实际设备所有者ID）
        if (xiaohongshuApps.length > 0) {
          for (const app of xiaohongshuApps) {
            await dbEnhancer.insertWithUserId('device_apps', {
              device_id: deviceId,
              app_type: 'xiaohongshu',
              app_name: app.text,
              app_text: app.text,
              app_bounds: app.bounds,
              is_clickable: app.clickable,
              detection_method: app.method
            }, actualOwnerId);
          }
          console.log(`[设备应用] 保存了 ${xiaohongshuApps.length} 个小红书应用 (设备所有者:用户${actualOwnerId})`);
        }

        // 插入闲鱼应用信息（使用实际设备所有者ID）
        if (xianyuApps.length > 0) {
          for (const app of xianyuApps) {
            await dbEnhancer.insertWithUserId('device_apps', {
              device_id: deviceId,
              app_type: 'xianyu',
              app_name: app.text,
              app_text: app.text,
              app_bounds: app.bounds,
              is_clickable: app.clickable,
              detection_method: app.method
            }, actualOwnerId);
          }
          console.log(`[设备应用] 保存了 ${xianyuApps.length} 个闲鱼应用 (设备所有者:用户${actualOwnerId})`);
        }

        res.json({
          success: true,
          message: '应用信息保存成功',
          data: {
            xiaohongshu: xiaohongshuApps.length,
            xianyu: xianyuApps.length
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: '数据库不可用'
        });
      }
    } catch (error) {
      console.error('保存应用信息失败:', error);
      res.status(500).json({
        success: false,
        message: '保存失败: ' + error.message
      });
    }
  });

  // 获取设备应用信息API (原始文件第10729行) - 已添加用户隔离
  app.get('/api/device/:deviceId/apps', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { deviceId } = req.params;
    const userId = req.currentUserId;

    console.log(`[用户${userId}] 查询设备应用信息: ${deviceId}`);

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '无权查看此设备的应用信息'
      });
    }

    try {
      if (pool) {
        const [rows] = await pool.execute(`
          SELECT app_type, app_name, app_text, app_bounds, is_clickable, detection_method, detected_at
          FROM device_apps
          WHERE device_id = ? AND user_id = ?
          ORDER BY app_type, detected_at DESC
        `, [deviceId, userId]);

        // 按应用类型分组
        const apps = {
          xiaohongshu: [],
          xianyu: []
        };

        rows.forEach(row => {
          const appInfo = {
            name: row.app_name,
            text: row.app_text,
            bounds: row.app_bounds,
            clickable: row.is_clickable,
            method: row.detection_method,
            detectedAt: row.detected_at
          };

          if (row.app_type === 'xiaohongshu') {
            apps.xiaohongshu.push(appInfo);
          } else if (row.app_type === 'xianyu') {
            apps.xianyu.push(appInfo);
          }
        });

        res.json({
          success: true,
          data: apps
        });
      } else {
        res.status(500).json({
          success: false,
          message: '数据库不可用'
        });
      }
    } catch (error) {
      console.error('查询应用信息失败:', error);
      res.status(500).json({
        success: false,
        message: '查询应用信息失败: ' + error.message
      });
    }
  });

  // 接收设备脚本状态API (原始文件第12625行) - 已添加用户隔离
  app.post('/api/device/:deviceId/script-status', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;
    const userId = req.currentUserId;

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '无权操作此设备'
      });
    }

    console.log(`🛑 [脚本状态] 用户${userId}的设备 ${deviceId} 脚本状态: ${status}`);
    console.log(`🛑 [脚本状态] 原因: ${reason}`);
    console.log(`🛑 [脚本状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备脚本状态变化
      io.emit('device_script_status', {
        deviceId: deviceId,
        status: status,
        reason: reason,
        message: message,
        timestamp: new Date().toISOString()
      });

      console.log(`🛑 [脚本状态] 已通知前端设备 ${deviceId} 脚本状态: ${status}`);

      res.json({
        success: true,
        message: '脚本状态已接收'
      });

    } catch (error) {
      console.error(`🛑 [脚本状态] 处理失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理脚本状态失败: ' + error.message
      });
    }
  });

  // 接收设备应用状态API (原始文件第12660行) - 已添加用户隔离
  app.post('/api/device/:deviceId/app-status', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;
    const userId = req.currentUserId;

    // 验证设备所属权
    const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '无权操作此设备'
      });
    }

    console.log(`📱 [应用状态] 用户${userId}的设备 ${deviceId} 应用状态: ${status}`);
    console.log(`📱 [应用状态] 原因: ${reason}`);
    console.log(`📱 [应用状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备应用状态变化
      io.emit('device_app_status', {
        deviceId: deviceId,
        status: status,
        reason: reason,
        message: message,
        timestamp: new Date().toISOString()
      });

      console.log(`📱 [应用状态] 已通知前端设备 ${deviceId} 应用状态: ${status}`);

      res.json({
        success: true,
        message: '应用状态已接收'
      });

    } catch (error) {
      console.error(`📱 [应用状态] 处理失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理应用状态失败: ' + error.message
      });
    }
  });

  // 接收设备清空私聊记录的响应API (原始文件第11244行)
  app.post('/api/device/clear-chat-records-response', (req, res) => {
    try {
      const { deviceId, success, message } = req.body;

      console.log('收到设备清空私聊记录响应:', {
        deviceId: deviceId,
        success: success,
        message: message
      });

      // 这里可以存储响应结果，供清空API使用
      // 由于我们使用的是同步等待机制，这个响应会被清空API的等待逻辑处理

      res.json({
        success: true,
        message: '响应已接收'
      });

    } catch (error) {
      console.error('处理设备清空响应失败:', error);
      res.status(500).json({
        success: false,
        message: '处理响应失败: ' + error.message
      });
    }
  });

  // 接收设备脚本完成确认的响应API (原始文件第11272行)
  app.post('/api/device/script-completion-ack', (req, res) => {
    try {
      const { deviceId, taskId, message, hasRunningScript } = req.body;

      console.log('收到设备脚本完成确认响应:', {
        deviceId: deviceId,
        taskId: taskId,
        message: message,
        hasRunningScript: hasRunningScript
      });

      // 如果设备端仍有脚本在运行，记录警告
      if (hasRunningScript) {
        console.warn(`⚠️ 设备 ${deviceId} 确认收到脚本完成通知，但仍有脚本在运行`);
      } else {
        console.log(`✅ 设备 ${deviceId} 确认脚本已完成，状态正常`);
      }

      res.json({
        success: true,
        message: '脚本完成确认已接收'
      });

    } catch (error) {
      console.error('处理设备脚本完成确认响应失败:', error);
      res.status(500).json({
        success: false,
        message: '处理确认响应失败'
      });
    }
  });

  // 设备状态监控和清理功能

  // 定期清理断开连接的设备记录
  setInterval(() => {
    const now = Date.now();
    const cleanupThreshold = 5 * 60 * 1000; // 5分钟

    for (const [deviceId, disconnectTime] of recentlyDisconnectedDevices) {
      if (now - disconnectTime > cleanupThreshold) {
        recentlyDisconnectedDevices.delete(deviceId);
        console.log(`清理过期的断开设备记录: ${deviceId}`);
      }
    }
  }, 60000); // 每分钟检查一次

  // 定期清理停止信号
  setInterval(() => {
    const now = Date.now();
    const signalTimeout = 10 * 60 * 1000; // 10分钟

    for (const [deviceId, signal] of deviceStopSignals) {
      if (now - signal.timestamp > signalTimeout) {
        deviceStopSignals.delete(deviceId);
        console.log(`清理过期的停止信号: ${deviceId}`);
      }
    }
  }, 5 * 60 * 1000); // 每5分钟检查一次

  // 设备健康检查功能
  async function performDeviceHealthCheck() {
    // 使用节流日志，避免频繁输出健康检查信息
    throttledLog('device_health_check', '🔍 执行设备健康检查...');

    const now = Date.now();
    const healthThreshold = 20 * 60 * 1000; // 增加到20分钟无活动视为不健康，给脚本执行留足时间
    const pollThreshold = 10 * 1000; // 10秒内有轮询活动视为活跃

    for (const [socketId, device] of devices) {
      const lastSeenTime = device.lastSeen ? device.lastSeen.getTime() : device.connectedAt.getTime();
      const lastPollTime = device.lastPollTime || lastSeenTime;
      const timeSinceLastSeen = now - lastSeenTime;
      const timeSinceLastPoll = now - lastPollTime;

      // 检查设备是否真的无活动（既没有lastSeen更新，也没有轮询活动）
      const isReallyInactive = timeSinceLastSeen > healthThreshold && timeSinceLastPoll > pollThreshold;

      if (isReallyInactive) {
        console.log(`⚠️ 设备健康检查：设备 ${device.deviceName} (${device.deviceId}) 已 ${Math.round(timeSinceLastSeen/1000)} 秒无活动，最后轮询: ${Math.round(timeSinceLastPoll/1000)} 秒前`);

        // 标记设备为可能离线
        if (device.status !== 'offline') {
          console.log(`📱 设备 ${device.deviceId} 健康检查失败，标记为离线`);

          // 在标记为离线前，先发送停止脚本命令
          console.log(`向设备 ${device.deviceId} 发送停止脚本命令（健康检查失败）`);
          try {
            // 检查是否是HTTP设备（socketId以http_开头或者在pendingCommands中有队列）
            const isHttpDevice = socketId.startsWith('http_') || (pendingCommands && pendingCommands.has(device.deviceId));

            if (isHttpDevice) {
              console.log(`设备 ${device.deviceId} 是HTTP连接设备，添加停止命令到队列`);

              // 对于HTTP设备，添加停止命令到pendingCommands队列
              if (pendingCommands) {
                // 如果设备队列不存在，创建一个新的队列
                if (!pendingCommands.has(device.deviceId)) {
                  pendingCommands.set(device.deviceId, []);
                  console.log(`为HTTP设备创建新的命令队列: ${device.deviceId}`);
                }

                pendingCommands.get(device.deviceId).push({
                  type: 'stop_script',
                  script: 'STOP_SCRIPT_COMMAND',
                  deviceId: device.deviceId,
                  reason: 'health_check_failed',
                  message: '设备健康检查失败，停止所有脚本',
                  timestamp: new Date().toISOString()
                });
                console.log(`已添加停止命令到HTTP设备队列: ${device.deviceId}`);
              } else {
                console.error('pendingCommands 未初始化，无法添加停止命令');
              }
            } else {
              // WebSocket设备：尝试发送停止命令
              console.log(`设备 ${device.deviceId} 是WebSocket连接设备，尝试发送停止命令`);

              const deviceSocket = io.sockets.sockets.get(socketId);
              if (deviceSocket && deviceSocket.connected) {
                // 发送停止脚本命令
                deviceSocket.emit('stop_script', {
                  reason: 'health_check_failed',
                  message: '设备健康检查失败，停止所有脚本',
                  timestamp: new Date().toISOString()
                });

                // 发送通用脚本命令停止
                deviceSocket.emit('script_command', {
                  type: 'stop_script',
                  deviceId: device.deviceId,
                  reason: 'health_check_failed',
                  message: '设备健康检查失败，停止所有脚本',
                  timestamp: new Date().toISOString()
                });
                console.log(`已向WebSocket设备 ${device.deviceId} 发送停止命令`);
              } else {
                console.log(`WebSocket设备 ${device.deviceId} 连接已断开，无法发送停止命令`);
              }
            }

            console.log(`已向设备 ${device.deviceId} 发送停止脚本命令`);
          } catch (error) {
            console.error(`向设备 ${device.deviceId} 发送停止脚本命令失败:`, error);
          }

          // 更新数据库中正在执行的任务状态为失败
          if (pool) {
            try {
              // 更新小红书执行日志
              await pool.execute(`
                UPDATE xiaohongshu_execution_logs
                SET execution_status = 'failed',
                    error_message = '设备离线，脚本执行失败',
                    completed_at = NOW(),
                    progress_percentage = 0
                WHERE device_id = ? AND execution_status IN ('running', 'pending')
              `, [device.deviceId]);

              // 更新闲鱼执行日志
              await pool.execute(`
                UPDATE xianyu_execution_logs
                SET execution_status = 'failed',
                    error_message = '设备离线，脚本执行失败',
                    completed_at = NOW(),
                    progress_percentage = 0
                WHERE device_id = ? AND execution_status IN ('running', 'pending')
              `, [device.deviceId]);

              console.log(`已更新设备 ${device.deviceId} 的执行日志状态为失败（设备离线）`);
            } catch (dbError) {
              console.error(`更新设备 ${device.deviceId} 执行日志失败:`, dbError);
            }
          }

          device.status = 'offline';

          // 更新数据库状态
          if (pool) {
            try {
              await pool.execute(`
                UPDATE devices SET status = 'offline', last_seen = NOW()
                WHERE device_id = ?
              `, [device.deviceId]);
              console.log(`数据库中设备状态已更新为离线: ${device.deviceId}`);
            } catch (dbError) {
              console.error('健康检查更新数据库状态失败:', dbError);
            }
          }

          // 通知Web客户端设备状态变化
          const statusChange = {
            type: 'device_health_warning',
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            lastSeen: device.lastSeen,
            timeSinceLastSeen: timeSinceLastSeen,
            timestamp: new Date()
          };

          // 发送多个事件确保前端能收到
          io.emit('device_status_changed', statusChange);
          io.emit('device_status_update', {
            deviceId: device.deviceId,
            status: 'offline',
            lastSeen: device.lastSeen || new Date()
          });

          // 通知前端清除该设备的脚本执行状态
          io.emit('clear_device_script_status', {
            deviceId: device.deviceId,
            reason: 'health_check_failed',
            message: '设备健康检查失败，清除脚本状态',
            timestamp: new Date().toISOString()
          });

          // 通知前端清除小红书和闲鱼的执行状态
          io.emit('xiaohongshu_execution_completed', {
            deviceId: device.deviceId,
            status: 'cancelled',
            message: '设备健康检查失败，任务已取消',
            timestamp: new Date().toISOString()
          });

          io.emit('xianyu_execution_completed', {
            deviceId: device.deviceId,
            status: 'cancelled',
            message: '设备健康检查失败，任务已取消',
            timestamp: new Date().toISOString()
          });

          console.log(`📡 已通知前端设备 ${device.deviceId} 状态变为离线`);
        }
      } else {
        // 设备活跃，确保状态正确
        if (device.status === 'offline' && (timeSinceLastPoll < pollThreshold)) {
          console.log(`📱 设备 ${device.deviceId} 恢复活跃，更新状态为在线`);
          device.status = 'online';

          // 更新数据库状态
          if (pool) {
            try {
              await pool.execute(`
                UPDATE devices SET status = 'online', last_seen = NOW()
                WHERE device_id = ?
              `, [device.deviceId]);
            } catch (dbError) {
              console.error('健康检查更新数据库状态失败:', dbError);
            }
          }

          // 通知前端设备恢复在线
          io.emit('device_status_update', {
            deviceId: device.deviceId,
            status: 'online',
            lastSeen: new Date()
          });
        }
      }
    }
  }

  // 每2分钟执行一次设备健康检查
  setInterval(performDeviceHealthCheck, 2 * 60 * 1000);

  // 设备统计信息功能
  function getDeviceStatistics() {
    const stats = {
      total: devices.size,
      online: 0,
      busy: 0,
      offline: 0,
      byType: {
        websocket: 0,
        http: 0,
        temp: 0
      },
      recentDisconnects: recentlyDisconnectedDevices.size,
      stopSignals: deviceStopSignals.size
    };

    for (const [socketId, device] of devices) {
      // 统计状态
      switch (device.status) {
        case 'online':
          stats.online++;
          break;
        case 'busy':
          stats.busy++;
          break;
        case 'offline':
          stats.offline++;
          break;
      }

      // 统计连接类型
      if (socketId.startsWith('http_')) {
        stats.byType.http++;
      } else if (socketId.startsWith('temp_')) {
        stats.byType.temp++;
      } else {
        stats.byType.websocket++;
      }
    }

    return stats;
  }

  // 设备统计API - 已添加用户隔离
  app.get('/api/device/statistics', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      // 获取用户的设备统计
      let userStats = {
        total: 0,
        online: 0,
        offline: 0,
        busy: 0,
        byType: {
          websocket: 0,
          http: 0,
          temp: 0
        },
        recentDisconnects: 0,
        stopSignals: 0
      };

      // 从数据库获取用户设备统计
      if (pool) {
        const [devices] = await pool.execute(`
          SELECT status, device_id FROM devices WHERE user_id = ?
        `, [userId]);

        userStats.total = devices.length;

        devices.forEach(device => {
          switch (device.status) {
            case 'online':
              userStats.online++;
              break;
            case 'busy':
              userStats.busy++;
              break;
            case 'offline':
              userStats.offline++;
              break;
          }
        });
      }

      res.json({
        success: true,
        data: userStats,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('获取设备统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取设备统计失败: ' + error.message
      });
    }
  });

  // 设备健康检查API - 已添加用户隔离
  app.get('/api/device/health-check', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      // 执行用户设备的健康检查
      performDeviceHealthCheck();

      // 获取用户设备统计
      let userStats = {
        total: 0,
        online: 0,
        offline: 0,
        busy: 0
      };

      if (pool) {
        const [devices] = await pool.execute(`
          SELECT status FROM devices WHERE user_id = ?
        `, [userId]);

        userStats.total = devices.length;
        devices.forEach(device => {
          switch (device.status) {
            case 'online':
              userStats.online++;
              break;
            case 'busy':
              userStats.busy++;
              break;
            case 'offline':
              userStats.offline++;
              break;
          }
        });
      }

      res.json({
        success: true,
        message: '设备健康检查已执行',
        data: userStats,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('设备健康检查失败:', error);
      res.status(500).json({
        success: false,
        message: '设备健康检查失败: ' + error.message
      });
    }
  });

  // 批量设备操作API
  app.post('/api/device/batch-operation', authenticateToken, async (req, res) => {
    try {
      const { operation, deviceIds, params } = req.body;

      if (!operation || !deviceIds || !Array.isArray(deviceIds)) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      const results = [];

      for (const deviceId of deviceIds) {
        try {
          let result = { deviceId, success: false, message: '' };

          switch (operation) {
            case 'disconnect':
              // 断开设备连接
              let deviceToDisconnect = null;
              let socketIdToDisconnect = null;

              for (const [socketId, device] of devices) {
                if (device.deviceId === deviceId) {
                  deviceToDisconnect = device;
                  socketIdToDisconnect = socketId;
                  break;
                }
              }

              if (deviceToDisconnect) {
                devices.delete(socketIdToDisconnect);
                result.success = true;
                result.message = '设备已断开';
              } else {
                result.message = '设备不存在';
              }
              break;

            case 'force_status':
              // 强制更新设备状态
              const newStatus = params?.status || 'online';
              await updateDeviceStatus(deviceId, newStatus);
              result.success = true;
              result.message = `状态已更新为 ${newStatus}`;
              break;

            case 'clear_commands':
              // 清理待执行命令
              if (pendingCommands.has(deviceId)) {
                pendingCommands.delete(deviceId);
              }
              if (deviceCommands[deviceId]) {
                delete deviceCommands[deviceId];
              }
              result.success = true;
              result.message = '待执行命令已清理';
              break;

            default:
              result.message = '不支持的操作类型';
          }

          results.push(result);
        } catch (error) {
          results.push({
            deviceId,
            success: false,
            message: error.message
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      res.json({
        success: true,
        message: `批量操作完成，成功: ${successCount}个，失败: ${failCount}个`,
        data: {
          operation,
          totalCount: deviceIds.length,
          successCount,
          failCount,
          results
        }
      });

    } catch (error) {
      console.error('批量设备操作失败:', error);
      res.status(500).json({
        success: false,
        message: '批量操作失败: ' + error.message
      });
    }
  });

  // 设备日志记录功能
  const deviceLogs = new Map(); // 存储设备日志

  function logDeviceActivity(deviceId, activity, details = {}) {
    if (!deviceLogs.has(deviceId)) {
      deviceLogs.set(deviceId, []);
    }

    const log = {
      timestamp: new Date(),
      activity,
      details,
      id: Date.now() + Math.random()
    };

    const logs = deviceLogs.get(deviceId);
    logs.push(log);

    // 保持最近100条日志
    if (logs.length > 100) {
      logs.shift();
    }

    console.log(`📝 设备活动日志: ${deviceId} - ${activity}`, details);
  }

  // 获取设备活动日志API
  app.get('/api/device/:deviceId/logs', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 50 } = req.query;

      const logs = deviceLogs.get(deviceId) || [];
      const recentLogs = logs.slice(-parseInt(limit));

      res.json({
        success: true,
        data: {
          deviceId,
          logs: recentLogs,
          totalCount: logs.length
        }
      });
    } catch (error) {
      console.error('获取设备日志失败:', error);
      res.status(500).json({
        success: false,
        message: '获取设备日志失败: ' + error.message
      });
    }
  });

  // 清理设备日志API
  app.delete('/api/device/:deviceId/logs', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;

      if (deviceLogs.has(deviceId)) {
        deviceLogs.delete(deviceId);
        logDeviceActivity(deviceId, 'logs_cleared', { by: 'admin' });
      }

      res.json({
        success: true,
        message: '设备日志已清理'
      });
    } catch (error) {
      console.error('清理设备日志失败:', error);
      res.status(500).json({
        success: false,
        message: '清理设备日志失败: ' + error.message
      });
    }
  });

  // 设备性能监控功能
  const devicePerformance = new Map(); // 存储设备性能数据

  function recordDevicePerformance(deviceId, performanceData) {
    if (!devicePerformance.has(deviceId)) {
      devicePerformance.set(deviceId, []);
    }

    const record = {
      timestamp: new Date(),
      ...performanceData
    };

    const records = devicePerformance.get(deviceId);
    records.push(record);

    // 保持最近50条性能记录
    if (records.length > 50) {
      records.shift();
    }

    logDeviceActivity(deviceId, 'performance_recorded', performanceData);
  }

  // 设备性能上报API
  app.post('/api/device/:deviceId/performance', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const performanceData = req.body;

      recordDevicePerformance(deviceId, performanceData);

      res.json({
        success: true,
        message: '性能数据已记录'
      });
    } catch (error) {
      console.error('记录设备性能失败:', error);
      res.status(500).json({
        success: false,
        message: '记录性能数据失败: ' + error.message
      });
    }
  });

  // 获取设备性能数据API
  app.get('/api/device/:deviceId/performance', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 20 } = req.query;

      const records = devicePerformance.get(deviceId) || [];
      const recentRecords = records.slice(-parseInt(limit));

      // 计算平均性能指标
      let avgCpuUsage = 0;
      let avgMemoryUsage = 0;
      let avgBatteryLevel = 0;

      if (recentRecords.length > 0) {
        avgCpuUsage = recentRecords.reduce((sum, r) => sum + (r.cpuUsage || 0), 0) / recentRecords.length;
        avgMemoryUsage = recentRecords.reduce((sum, r) => sum + (r.memoryUsage || 0), 0) / recentRecords.length;
        avgBatteryLevel = recentRecords.reduce((sum, r) => sum + (r.batteryLevel || 0), 0) / recentRecords.length;
      }

      res.json({
        success: true,
        data: {
          deviceId,
          records: recentRecords,
          totalCount: records.length,
          averages: {
            cpuUsage: Math.round(avgCpuUsage * 100) / 100,
            memoryUsage: Math.round(avgMemoryUsage * 100) / 100,
            batteryLevel: Math.round(avgBatteryLevel * 100) / 100
          }
        }
      });
    } catch (error) {
      console.error('获取设备性能数据失败:', error);
      res.status(500).json({
        success: false,
        message: '获取性能数据失败: ' + error.message
      });
    }
  });

  // 设备命令历史记录功能
  const deviceCommandHistory = new Map(); // 存储设备命令历史

  function recordDeviceCommand(deviceId, command, source = 'unknown') {
    if (!deviceCommandHistory.has(deviceId)) {
      deviceCommandHistory.set(deviceId, []);
    }

    const record = {
      timestamp: new Date(),
      command: typeof command === 'object' ? JSON.stringify(command) : command,
      source,
      id: Date.now() + Math.random()
    };

    const history = deviceCommandHistory.get(deviceId);
    history.push(record);

    // 保持最近200条命令历史
    if (history.length > 200) {
      history.shift();
    }

    logDeviceActivity(deviceId, 'command_sent', { source, commandType: command.type || 'unknown' });
  }

  // 获取设备命令历史API
  app.get('/api/device/:deviceId/command-history', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 50, source } = req.query;

      let history = deviceCommandHistory.get(deviceId) || [];

      // 按来源过滤
      if (source) {
        history = history.filter(h => h.source === source);
      }

      const recentHistory = history.slice(-parseInt(limit));

      res.json({
        success: true,
        data: {
          deviceId,
          history: recentHistory,
          totalCount: history.length
        }
      });
    } catch (error) {
      console.error('获取设备命令历史失败:', error);
      res.status(500).json({
        success: false,
        message: '获取命令历史失败: ' + error.message
      });
    }
  });

  // 设备连接质量监控功能
  const deviceConnectionQuality = new Map(); // 存储设备连接质量数据

  function recordConnectionQuality(deviceId, qualityData) {
    if (!deviceConnectionQuality.has(deviceId)) {
      deviceConnectionQuality.set(deviceId, []);
    }

    const record = {
      timestamp: new Date(),
      ...qualityData
    };

    const records = deviceConnectionQuality.get(deviceId);
    records.push(record);

    // 保持最近100条连接质量记录
    if (records.length > 100) {
      records.shift();
    }

    // 分析连接质量趋势
    if (records.length >= 5) {
      const recentRecords = records.slice(-5);
      const avgLatency = recentRecords.reduce((sum, r) => sum + (r.latency || 0), 0) / recentRecords.length;
      const avgPacketLoss = recentRecords.reduce((sum, r) => sum + (r.packetLoss || 0), 0) / recentRecords.length;

      if (avgLatency > 1000 || avgPacketLoss > 5) {
        console.warn(`⚠️ 设备 ${deviceId} 连接质量较差: 平均延迟 ${avgLatency}ms, 丢包率 ${avgPacketLoss}%`);

        // 通知Web客户端连接质量警告
        io.emit('device_connection_warning', {
          deviceId,
          avgLatency,
          avgPacketLoss,
          timestamp: new Date()
        });
      }
    }
  }

  // 设备连接质量上报API
  app.post('/api/device/:deviceId/connection-quality', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const qualityData = req.body;

      recordConnectionQuality(deviceId, qualityData);
      logDeviceActivity(deviceId, 'connection_quality_reported', qualityData);

      res.json({
        success: true,
        message: '连接质量数据已记录'
      });
    } catch (error) {
      console.error('记录连接质量失败:', error);
      res.status(500).json({
        success: false,
        message: '记录连接质量失败: ' + error.message
      });
    }
  });

  // 获取设备连接质量API
  app.get('/api/device/:deviceId/connection-quality', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 20 } = req.query;

      const records = deviceConnectionQuality.get(deviceId) || [];
      const recentRecords = records.slice(-parseInt(limit));

      // 计算连接质量统计
      let avgLatency = 0;
      let avgPacketLoss = 0;
      let maxLatency = 0;
      let minLatency = Infinity;

      if (recentRecords.length > 0) {
        avgLatency = recentRecords.reduce((sum, r) => sum + (r.latency || 0), 0) / recentRecords.length;
        avgPacketLoss = recentRecords.reduce((sum, r) => sum + (r.packetLoss || 0), 0) / recentRecords.length;
        maxLatency = Math.max(...recentRecords.map(r => r.latency || 0));
        minLatency = Math.min(...recentRecords.map(r => r.latency || 0));

        if (minLatency === Infinity) minLatency = 0;
      }

      res.json({
        success: true,
        data: {
          deviceId,
          records: recentRecords,
          totalCount: records.length,
          statistics: {
            avgLatency: Math.round(avgLatency * 100) / 100,
            avgPacketLoss: Math.round(avgPacketLoss * 100) / 100,
            maxLatency,
            minLatency
          }
        }
      });
    } catch (error) {
      console.error('获取连接质量数据失败:', error);
      res.status(500).json({
        success: false,
        message: '获取连接质量数据失败: ' + error.message
      });
    }
  });

  // 设备错误报告功能
  const deviceErrors = new Map(); // 存储设备错误报告

  function recordDeviceError(deviceId, errorData) {
    if (!deviceErrors.has(deviceId)) {
      deviceErrors.set(deviceId, []);
    }

    const error = {
      timestamp: new Date(),
      id: Date.now() + Math.random(),
      ...errorData
    };

    const errors = deviceErrors.get(deviceId);
    errors.push(error);

    // 保持最近50条错误记录
    if (errors.length > 50) {
      errors.shift();
    }

    console.error(`❌ 设备错误报告: ${deviceId} - ${errorData.type}: ${errorData.message}`);
    logDeviceActivity(deviceId, 'error_reported', errorData);

    // 如果是严重错误，立即通知Web客户端
    if (errorData.severity === 'critical') {
      io.emit('device_critical_error', {
        deviceId,
        error: errorData,
        timestamp: new Date()
      });
    }
  }

  // 设备错误上报API
  app.post('/api/device/:deviceId/error', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const errorData = req.body;

      recordDeviceError(deviceId, errorData);

      res.json({
        success: true,
        message: '错误报告已记录'
      });
    } catch (error) {
      console.error('记录设备错误失败:', error);
      res.status(500).json({
        success: false,
        message: '记录错误报告失败: ' + error.message
      });
    }
  });

  // 获取设备错误报告API
  app.get('/api/device/:deviceId/errors', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 20, severity } = req.query;

      let errors = deviceErrors.get(deviceId) || [];

      // 按严重程度过滤
      if (severity) {
        errors = errors.filter(e => e.severity === severity);
      }

      const recentErrors = errors.slice(-parseInt(limit));

      // 统计错误类型
      const errorTypes = {};
      errors.forEach(error => {
        const type = error.type || 'unknown';
        errorTypes[type] = (errorTypes[type] || 0) + 1;
      });

      res.json({
        success: true,
        data: {
          deviceId,
          errors: recentErrors,
          totalCount: errors.length,
          errorTypes
        }
      });
    } catch (error) {
      console.error('获取设备错误报告失败:', error);
      res.status(500).json({
        success: false,
        message: '获取错误报告失败: ' + error.message
      });
    }
  });

  // 设备配置管理功能
  const deviceConfigurations = new Map(); // 存储设备配置

  function saveDeviceConfiguration(deviceId, config) {
    deviceConfigurations.set(deviceId, {
      ...config,
      lastUpdated: new Date(),
      version: (deviceConfigurations.get(deviceId)?.version || 0) + 1
    });

    logDeviceActivity(deviceId, 'configuration_updated', { configKeys: Object.keys(config) });
    console.log(`📝 设备配置已保存: ${deviceId}`);
  }

  function getDeviceConfiguration(deviceId) {
    return deviceConfigurations.get(deviceId) || {};
  }

  // 保存设备配置API
  app.post('/api/device/:deviceId/config', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const config = req.body;

      saveDeviceConfiguration(deviceId, config);

      res.json({
        success: true,
        message: '设备配置已保存'
      });
    } catch (error) {
      console.error('保存设备配置失败:', error);
      res.status(500).json({
        success: false,
        message: '保存设备配置失败: ' + error.message
      });
    }
  });

  // 获取设备配置API
  app.get('/api/device/:deviceId/config', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const config = getDeviceConfiguration(deviceId);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      console.error('获取设备配置失败:', error);
      res.status(500).json({
        success: false,
        message: '获取设备配置失败: ' + error.message
      });
    }
  });

  // 设备远程控制功能
  const deviceRemoteCommands = new Map(); // 存储远程控制命令

  function sendRemoteCommand(deviceId, command) {
    if (!deviceRemoteCommands.has(deviceId)) {
      deviceRemoteCommands.set(deviceId, []);
    }

    const remoteCommand = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      command,
      status: 'pending'
    };

    deviceRemoteCommands.get(deviceId).push(remoteCommand);
    recordDeviceCommand(deviceId, command, 'remote_control');

    // 发送命令到设备
    let device = null;
    for (const [socketId, deviceData] of devices) {
      if (deviceData.deviceId === deviceId) {
        device = deviceData;
        break;
      }
    }

    if (device) {
      if (device.socketId.startsWith('http_')) {
        // HTTP设备：添加到待执行队列
        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }
        pendingCommands.get(deviceId).push({
          type: 'remote_command',
          command: command,
          commandId: remoteCommand.id,
          timestamp: Date.now()
        });
      } else {
        // WebSocket设备：直接发送
        const deviceSocket = io.sockets.sockets.get(device.socketId);
        if (deviceSocket) {
          deviceSocket.emit('remote_command', {
            commandId: remoteCommand.id,
            command: command,
            timestamp: Date.now()
          });
        }
      }

      remoteCommand.status = 'sent';
      console.log(`📡 远程命令已发送到设备 ${deviceId}: ${command.type}`);
    } else {
      remoteCommand.status = 'failed';
      remoteCommand.error = '设备不在线';
      console.error(`❌ 设备 ${deviceId} 不在线，无法发送远程命令`);
    }

    return remoteCommand;
  }

  // 发送远程控制命令API
  app.post('/api/device/:deviceId/remote-command', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const command = req.body;

      if (!command.type) {
        return res.status(400).json({
          success: false,
          message: '缺少命令类型'
        });
      }

      const result = sendRemoteCommand(deviceId, command);

      res.json({
        success: true,
        message: '远程命令已发送',
        data: result
      });
    } catch (error) {
      console.error('发送远程命令失败:', error);
      res.status(500).json({
        success: false,
        message: '发送远程命令失败: ' + error.message
      });
    }
  });

  // 获取远程命令状态API
  app.get('/api/device/:deviceId/remote-commands', authenticateToken, (req, res) => {
    try {
      const { deviceId } = req.params;
      const { limit = 20 } = req.query;

      const commands = deviceRemoteCommands.get(deviceId) || [];
      const recentCommands = commands.slice(-parseInt(limit));

      res.json({
        success: true,
        data: {
          deviceId,
          commands: recentCommands,
          totalCount: commands.length
        }
      });
    } catch (error) {
      console.error('获取远程命令状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取远程命令状态失败: ' + error.message
      });
    }
  });

  // 设备分组管理功能
  const deviceGroups = new Map(); // 存储设备分组

  function createDeviceGroup(groupName, deviceIds, description = '') {
    const group = {
      id: Date.now() + Math.random(),
      name: groupName,
      deviceIds: [...deviceIds],
      description,
      createdAt: new Date(),
      lastUpdated: new Date()
    };

    deviceGroups.set(group.id, group);
    console.log(`📁 设备分组已创建: ${groupName}, 包含 ${deviceIds.length} 个设备`);

    return group;
  }

  function updateDeviceGroup(groupId, updates) {
    const group = deviceGroups.get(groupId);
    if (!group) {
      throw new Error('分组不存在');
    }

    Object.assign(group, updates, { lastUpdated: new Date() });
    console.log(`📁 设备分组已更新: ${group.name}`);

    return group;
  }

  function deleteDeviceGroup(groupId) {
    const group = deviceGroups.get(groupId);
    if (!group) {
      throw new Error('分组不存在');
    }

    deviceGroups.delete(groupId);
    console.log(`📁 设备分组已删除: ${group.name}`);

    return true;
  }

  // 创建设备分组API
  app.post('/api/device/groups', authenticateToken, (req, res) => {
    try {
      const { name, deviceIds, description } = req.body;

      if (!name || !deviceIds || !Array.isArray(deviceIds)) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      const group = createDeviceGroup(name, deviceIds, description);

      res.json({
        success: true,
        message: '设备分组创建成功',
        data: group
      });
    } catch (error) {
      console.error('创建设备分组失败:', error);
      res.status(500).json({
        success: false,
        message: '创建设备分组失败: ' + error.message
      });
    }
  });

  // 获取设备分组列表API
  app.get('/api/device/groups', authenticateToken, (req, res) => {
    try {
      const groups = Array.from(deviceGroups.values());

      res.json({
        success: true,
        data: groups
      });
    } catch (error) {
      console.error('获取设备分组失败:', error);
      res.status(500).json({
        success: false,
        message: '获取设备分组失败: ' + error.message
      });
    }
  });

  // 更新设备分组API
  app.put('/api/device/groups/:groupId', authenticateToken, (req, res) => {
    try {
      const { groupId } = req.params;
      const updates = req.body;

      const group = updateDeviceGroup(parseFloat(groupId), updates);

      res.json({
        success: true,
        message: '设备分组更新成功',
        data: group
      });
    } catch (error) {
      console.error('更新设备分组失败:', error);
      res.status(500).json({
        success: false,
        message: '更新设备分组失败: ' + error.message
      });
    }
  });

  // 删除设备分组API
  app.delete('/api/device/groups/:groupId', authenticateToken, (req, res) => {
    try {
      const { groupId } = req.params;

      deleteDeviceGroup(parseFloat(groupId));

      res.json({
        success: true,
        message: '设备分组删除成功'
      });
    } catch (error) {
      console.error('删除设备分组失败:', error);
      res.status(500).json({
        success: false,
        message: '删除设备分组失败: ' + error.message
      });
    }
  });

  console.log('✅ 设备管理模块设置完成');

  // 返回设备管理相关函数供其他模块使用
  return {
    updateDeviceStatus,
    getDeviceStatistics,
    performDeviceHealthCheck,
    logDeviceActivity,
    recordDevicePerformance,
    recordDeviceCommand,
    recordConnectionQuality,
    recordDeviceError,
    saveDeviceConfiguration,
    getDeviceConfiguration,
    sendRemoteCommand,
    createDeviceGroup,
    updateDeviceGroup,
    deleteDeviceGroup
  };
}

module.exports = { setupServerDevice };
