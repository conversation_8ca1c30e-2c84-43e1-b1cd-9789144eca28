const { pool } = require('../config/database');

class XianyuLogService {

  // 安全的JSON解析
  safeJsonParse(jsonString, defaultValue = null) {
    if (!jsonString) return defaultValue;

    try {
      // 如果已经是对象，直接返回
      if (typeof jsonString === 'object') {
        return jsonString;
      }

      // 尝试解析JSON字符串
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn('JSON解析失败:', error.message, '原始数据:', jsonString);
      return defaultValue;
    }
  }

  // 获取功能名称
  getFunctionName(functionType) {
    const nameMap = {
      'keywordMessage': '关键词私信'
    };
    return nameMap[functionType] || functionType;
  }

  // 创建执行日志记录
  async createExecutionLog(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig = null, userId = null) {
    try {
      // 从配置参数中提取选择的应用名称
      const selectedApp = configParams && configParams.selectedApp ? configParams.selectedApp : '';

      const [result] = await pool.execute(`
        INSERT INTO xianyu_execution_logs
        (task_id, function_type, device_id, device_name, function_name, selected_app, config_params, schedule_config, execution_status, started_at, user_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW(), ?)
      `, [
        taskId,
        functionType,
        deviceId,
        deviceName,
        this.getFunctionName(functionType),
        selectedApp,
        JSON.stringify(configParams),
        scheduleConfig ? JSON.stringify(scheduleConfig) : null,
        userId
      ]);

      console.log(`闲鱼执行日志已创建: ${taskId}, ID: ${result.insertId}, 选择的应用: ${selectedApp || '默认'}, 用户: ${userId}`);
      return result.insertId;
    } catch (error) {
      console.error('创建闲鱼执行日志失败:', error);
      throw error;
    }
  }

  // 带用户ID的创建执行日志记录（新方法）
  async createExecutionLogWithUserId(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig = null, userId) {
    return await this.createExecutionLog(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig, userId);
  }

  // 更新执行状态
  async updateExecutionStatus(taskId, status, progress = null, currentStage = null, debugLogs = null, errorMessage = null) {
    try {
      let updateFields = [];
      let params = [];

      // 只有当status不为null时才更新execution_status字段
      if (status !== null) {
        updateFields.push('execution_status = ?');
        params.push(status);
      }

      if (progress !== null) {
        updateFields.push('progress_percentage = ?');
        params.push(progress);
      }

      // 注意：数据库表中没有current_stage字段，将其作为调试日志记录
      if (currentStage !== null) {
        // 将stage信息作为调试日志记录
        updateFields.push('execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?)');
        params.push(JSON.stringify({
          timestamp: new Date().toISOString(),
          type: 'stage_update',
          message: currentStage
        }));
      }

      if (debugLogs !== null) {
        // 将新的调试日志添加到现有的日志中
        updateFields.push('execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?)');
        params.push(JSON.stringify({
          timestamp: new Date().toISOString(),
          message: debugLogs
        }));
      }

      if (errorMessage !== null) {
        updateFields.push('error_message = ?');
        params.push(errorMessage);
      }

      // 如果状态是完成或失败，更新完成时间和执行时长
      if (status === 'completed' || status === 'failed' || status === 'stopped' || status === 'error') {
        updateFields.push('completed_at = NOW()');
        updateFields.push('execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())');
      }

      // 如果没有字段需要更新，直接返回
      if (updateFields.length === 0) {
        console.log(`闲鱼执行状态更新跳过: ${taskId} (没有字段需要更新)`);
        return true;
      }

      params.push(taskId);

      const [result] = await pool.execute(`
        UPDATE xianyu_execution_logs
        SET ${updateFields.join(', ')}
        WHERE task_id = ?
      `, params);

      console.log(`闲鱼执行状态已更新: ${taskId} -> ${status} (${progress}%)`);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新闲鱼执行状态失败:', error);
      throw error;
    }
  }

  // 获取执行日志列表
  async getExecutionLogs(page = 1, limit = 20, filters = {}) {
    try {
      return await this.getExecutionLogsWithUserFilter(page, limit, filters);
    } catch (error) {
      console.error('获取闲鱼执行日志失败:', error);
      throw error;
    }
  }

  // 带用户过滤的获取执行日志列表
  async getExecutionLogsWithUserFilter(page = 1, limit = 20, filters = {}) {
    try {
      let whereConditions = [];
      let params = [];

      // 添加用户过滤条件（必须）
      if (filters.userId) {
        whereConditions.push('user_id = ?');
        params.push(filters.userId);
      }

      // 构建WHERE条件
      if (filters.functionType && filters.functionType.trim() !== '') {
        whereConditions.push('function_type = ?');
        params.push(filters.functionType);
      }

      if (filters.deviceId && filters.deviceId.trim() !== '') {
        whereConditions.push('device_id = ?');
        params.push(filters.deviceId);
      }

      if (filters.executionStatus && filters.executionStatus !== 'undefined' && filters.executionStatus.trim() !== '') {
        whereConditions.push('execution_status = ?');
        params.push(filters.executionStatus);
      }

      if (filters.startDate) {
        whereConditions.push('started_at >= ?');
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        whereConditions.push('started_at <= ?');
        params.push(filters.endDate);
      }

      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

      // 获取总数
      const [countResult] = await pool.execute(`
        SELECT COUNT(*) as total FROM xianyu_execution_logs ${whereClause}
      `, params);

      const total = countResult[0].total;

      // 获取分页数据
      const offset = (page - 1) * limit;

      // 构建完整的查询参数数组（为主查询添加limit和offset参数）
      const queryParams = [...params, limit, offset];

      const [rows] = await pool.execute(`
        SELECT
          id,
          task_id,
          function_type,
          device_id,
          device_name,
          function_name,
          selected_app,
          config_params,
          schedule_config,
          execution_status,
          progress_percentage,
          execution_logs,
          error_message,
          started_at,
          completed_at,
          execution_duration
        FROM xianyu_execution_logs
        ${whereClause}
        ORDER BY started_at DESC
        LIMIT ? OFFSET ?
      `, queryParams);

      // 处理数据格式
      const logs = rows.map(row => {
        const configParams = this.safeJsonParse(row.config_params, {});
        // 如果数据库中有selected_app字段，优先使用；否则从configParams中获取
        if (row.selected_app && !configParams.selectedApp) {
          configParams.selectedApp = row.selected_app;
        }

        return {
          id: row.task_id,
          functionType: row.function_type,
          deviceInfo: {
            id: row.device_id,
            name: row.device_name,
            ip: '*************' // 默认IP，实际应该从设备信息中获取
          },
          functionName: row.function_name,
          selectedApp: row.selected_app || configParams.selectedApp || '默认',
          configParams: configParams,
          scheduleConfig: this.safeJsonParse(row.schedule_config, null),
        executionStatus: row.execution_status,
        progress: row.progress_percentage || 0,
        stage: this.getStageFromLogs(row.execution_logs),
        executionLogs: this.safeJsonParse(row.execution_logs, []),
        errorMessage: row.error_message,
        startedAt: row.started_at,
        completedAt: row.completed_at,
        executionDuration: row.execution_duration,
          // 添加缺少的字段
          createdAt: row.started_at, // 任务创建时间
          executionResult: row.error_message ? '执行失败' : (row.execution_status === 'completed' ? '执行成功' : '执行中'), // 任务结果
          executionTime: row.execution_duration || 0 // 任务执行时长（秒）
        };
      });

      return {
        logs,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取闲鱼执行日志失败:', error);
      throw error;
    }
  }

  // 从执行日志中提取最新的阶段信息
  getStageFromLogs(executionLogsJson) {
    try {
      if (!executionLogsJson) return '未知';

      const logs = this.safeJsonParse(executionLogsJson, []);
      if (!Array.isArray(logs) || logs.length === 0) return '未知';

      // 查找最新的stage_update类型的日志
      for (let i = logs.length - 1; i >= 0; i--) {
        const log = logs[i];
        if (log.type === 'stage_update' && log.message) {
          return log.message;
        }
      }

      return '执行中';
    } catch (error) {
      console.warn('解析执行日志阶段信息失败:', error.message);
      return '未知';
    }
  }

  // 获取执行日志详情
  async getExecutionLogDetail(taskId) {
    try {
      const [rows] = await pool.execute(`
        SELECT
          id,
          task_id,
          function_type,
          device_id,
          device_name,
          function_name,
          selected_app,
          config_params,
          schedule_config,
          execution_status,
          progress_percentage,
          execution_logs,
          error_message,
          started_at,
          completed_at,
          execution_duration
        FROM xianyu_execution_logs
        WHERE task_id = ?
      `, [taskId]);

      if (rows.length === 0) {
        return null;
      }

      const row = rows[0];
      const configParams = this.safeJsonParse(row.config_params, {});
      // 如果数据库中有selected_app字段，优先使用；否则从configParams中获取
      if (row.selected_app && !configParams.selectedApp) {
        configParams.selectedApp = row.selected_app;
      }

      return {
        id: row.task_id,
        functionType: row.function_type,
        deviceInfo: {
          id: row.device_id,
          name: row.device_name,
          ip: '*************'
        },
        functionName: row.function_name,
        selectedApp: row.selected_app || configParams.selectedApp || '默认',
        configParams: configParams,
        scheduleConfig: this.safeJsonParse(row.schedule_config, null),
        executionStatus: row.execution_status,
        progress: row.progress_percentage || 0,
        stage: this.getStageFromLogs(row.execution_logs),
        executionLogs: this.safeJsonParse(row.execution_logs, []),
        errorMessage: row.error_message,
        startedAt: row.started_at,
        completedAt: row.completed_at,
        executionDuration: row.execution_duration
      };
    } catch (error) {
      console.error('获取闲鱼执行日志详情失败:', error);
      throw error;
    }
  }

  // 清空所有执行日志
  async clearAllExecutionLogs() {
    try {
      const [result] = await pool.execute('DELETE FROM xianyu_execution_logs');
      console.log(`已清空所有闲鱼执行日志，删除了 ${result.affectedRows} 条记录`);
      return result.affectedRows;
    } catch (error) {
      console.error('清空闲鱼执行日志失败:', error);
      throw error;
    }
  }

  // 批量更新所有正在执行的任务状态为已停止
  async updateAllRunningTasksToStopped(reason = '用户手动停止') {
    try {
      const [result] = await pool.execute(`
        UPDATE xianyu_execution_logs
        SET execution_status = 'stopped',
            progress_percentage = 0,
            execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?),
            completed_at = NOW(),
            execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
        WHERE execution_status IN ('pending', 'running', 'executing')
      `, [JSON.stringify({
        timestamp: new Date().toISOString(),
        message: reason
      })]);

      console.log(`批量更新闲鱼任务状态完成，影响行数: ${result.affectedRows}`);
      return result.affectedRows;
    } catch (error) {
      console.error('批量更新闲鱼任务状态失败:', error);
      throw error;
    }
  }
}

module.exports = new XianyuLogService();
