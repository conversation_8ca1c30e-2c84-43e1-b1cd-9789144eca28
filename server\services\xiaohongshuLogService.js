const { pool } = require('../config/database');

class XiaohongshuLogService {

  // 安全的JSON解析方法
  safeJsonParse(jsonData, defaultValue = null) {
    if (!jsonData) return defaultValue;

    try {
      // 如果已经是对象，直接返回
      if (typeof jsonData === 'object') {
        return jsonData;
      }

      // 如果是字符串，尝试解析
      if (typeof jsonData === 'string') {
        // 检查是否是 [object Object] 这样的无效字符串
        if (jsonData === '[object Object]' || jsonData === 'undefined' || jsonData === 'null') {
          console.warn('检测到无效的JSON字符串:', jsonData);
          return defaultValue;
        }
        return JSON.parse(jsonData);
      }

      // 其他类型直接返回默认值
      return defaultValue;
    } catch (error) {
      console.warn('JSON解析失败:', error.message, '原始数据:', jsonData);
      return defaultValue;
    }
  }

  // 获取功能名称
  getFunctionName(functionType) {
    const nameMap = {
      'profile': '修改资料',
      'searchGroupChat': '搜索加群',
      'groupMessage': '循环群发',
      'articleComment': '文章评论',
      'uidMessage': '手动输入UID私信',
      'uidFileMessage': '文件上传UID私信',
      'videoPublish': '发布视频'
    };
    return nameMap[functionType] || functionType;
  }

  // 创建执行日志记录
  async createExecutionLog(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig = null, userId = null) {
    try {
      // 从配置参数中提取选择的应用名称
      const selectedApp = configParams && configParams.selectedApp ? configParams.selectedApp : '';

      const [result] = await pool.execute(`
        INSERT INTO xiaohongshu_execution_logs
        (task_id, function_type, device_id, device_name, function_name, selected_app, config_params, schedule_config, execution_status, started_at, user_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW(), ?)
      `, [
        taskId,
        functionType,
        deviceId,
        deviceName,
        this.getFunctionName(functionType),
        selectedApp,
        JSON.stringify(configParams),
        scheduleConfig ? JSON.stringify(scheduleConfig) : null,
        userId
      ]);

      console.log(`小红书执行日志已创建: ${taskId}, ID: ${result.insertId}, 选择的应用: ${selectedApp || '默认'}, 用户: ${userId}`);
      return result.insertId;
    } catch (error) {
      console.error('创建小红书执行日志失败:', error);
      throw error;
    }
  }

  // 带用户ID的创建执行日志记录（新方法）
  async createExecutionLogWithUserId(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig = null, userId) {
    return await this.createExecutionLog(taskId, functionType, deviceId, deviceName, configParams, scheduleConfig, userId);
  }

  // 更新执行状态
  async updateExecutionStatus(taskId, status, progress = null, currentStage = null, debugLogs = null, errorMessage = null) {
    try {
      let updateFields = [];
      let params = [];

      // 只有当status不为null时才更新execution_status字段
      if (status !== null) {
        updateFields.push('execution_status = ?');
        params.push(status);
      }

      if (progress !== null) {
        updateFields.push('progress_percentage = ?');
        params.push(progress);
      }

      // 注意：数据库表中没有current_stage字段，将其作为调试日志记录
      if (currentStage !== null) {
        // 将stage信息作为调试日志记录
        updateFields.push('execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?)');
        params.push(JSON.stringify({
          timestamp: new Date().toISOString(),
          type: 'stage_update',
          message: currentStage
        }));
      }

      if (debugLogs !== null) {
        // 将新的调试日志添加到现有的日志中
        updateFields.push('execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?)');
        params.push(JSON.stringify({
          timestamp: new Date().toISOString(),
          message: debugLogs
        }));
      }

      if (errorMessage !== null) {
        updateFields.push('error_message = ?');
        params.push(errorMessage);
      }

      // 如果状态是完成或失败，更新完成时间和执行时长
      if (status === 'completed' || status === 'failed' || status === 'stopped') {
        updateFields.push('completed_at = NOW()');
        updateFields.push('execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())');
      }

      // 如果没有字段需要更新，直接返回
      if (updateFields.length === 0) {
        console.log(`小红书执行状态更新跳过: ${taskId} (没有字段需要更新)`);
        return true;
      }

      params.push(taskId);

      const [result] = await pool.execute(`
        UPDATE xiaohongshu_execution_logs
        SET ${updateFields.join(', ')}
        WHERE task_id = ?
      `, params);

      console.log(`小红书执行状态已更新: ${taskId} -> ${status}`);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新小红书执行状态失败:', error);
      throw error;
    }
  }

  // 更新执行结果
  async updateExecutionResult(taskId, result) {
    try {
      const [updateResult] = await pool.execute(`
        UPDATE xiaohongshu_execution_logs
        SET execution_result = ?
        WHERE task_id = ?
      `, [JSON.stringify(result), taskId]);

      console.log(`小红书执行结果已更新: ${taskId}`);
      return updateResult.affectedRows > 0;
    } catch (error) {
      console.error('更新小红书执行结果失败:', error);
      throw error;
    }
  }

  // 添加执行日志
  async appendExecutionLog(taskId, logMessage) {
    try {
      const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
      const formattedLog = `[${timestamp}] ${logMessage}`;

      await this.updateExecutionStatus(taskId, null, null, formattedLog);
      console.log(`小红书执行日志已添加: ${taskId} - ${logMessage}`);
    } catch (error) {
      console.error('添加小红书执行日志失败:', error);
      throw error;
    }
  }

  // 获取执行日志列表
  async getExecutionLogs(page = 1, limit = 20, filters = {}) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = '';
      let params = [];

      // 构建筛选条件
      const conditions = [];

      if (filters.functionType && filters.functionType.trim() !== '') {
        conditions.push('function_type = ?');
        params.push(filters.functionType);
      }

      if (filters.deviceId && filters.deviceId.trim() !== '') {
        conditions.push('device_id = ?');
        params.push(filters.deviceId);
      }

      if (filters.executionStatus && filters.executionStatus !== 'undefined' && filters.executionStatus.trim() !== '') {
        conditions.push('execution_status = ?');
        params.push(filters.executionStatus);
      }

      if (filters.startDate) {
        conditions.push('started_at >= ?');
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push('started_at <= ?');
        params.push(filters.endDate);
      }

      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }

      return await this.getExecutionLogsWithUserFilter(page, limit, filters);
    } catch (error) {
      console.error('获取小红书执行日志失败:', error);
      throw error;
    }
  }

  // 带用户过滤的获取执行日志列表
  async getExecutionLogsWithUserFilter(page = 1, limit = 20, filters = {}) {
    try {
      const offset = (page - 1) * limit;
      let whereClause = '';
      const conditions = [];
      const params = [];

      // 添加用户过滤条件（必须）
      if (filters.userId) {
        conditions.push('user_id = ?');
        params.push(filters.userId);
      }

      if (filters.functionType && filters.functionType.trim() !== '') {
        conditions.push('function_type = ?');
        params.push(filters.functionType);
      }

      if (filters.executionStatus && filters.executionStatus !== 'undefined' && filters.executionStatus.trim() !== '') {
        conditions.push('execution_status = ?');
        params.push(filters.executionStatus);
      }

      if (filters.deviceId && filters.deviceId.trim() !== '') {
        conditions.push('device_id = ?');
        params.push(filters.deviceId);
      }

      if (filters.startDate) {
        conditions.push('started_at >= ?');
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push('started_at <= ?');
        params.push(filters.endDate);
      }

      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }

      // 获取总数
      const [countResult] = await pool.execute(`
        SELECT COUNT(*) as total
        FROM xiaohongshu_execution_logs
        ${whereClause}
      `, params);

      const total = countResult[0].total;

      // 获取日志列表
      const [logs] = await pool.execute(`
        SELECT
          id, task_id, function_type, device_id, device_name, function_name, selected_app,
          config_params, schedule_config, execution_status,
          progress_percentage as progress, execution_result, execution_logs,
          error_message, started_at, completed_at, execution_duration
        FROM xiaohongshu_execution_logs
        ${whereClause}
        ORDER BY started_at DESC
        LIMIT ? OFFSET ?
      `, [...params, limit || 20, offset || 0]);

      // 解析JSON字段，同时保持原始字段名和格式化字段名的兼容性
      const processedLogs = logs.map(log => ({
        // 原始数据库字段名（用于小红书执行日志页面）
        task_id: log.task_id,
        function_type: log.function_type,
        function_name: log.function_name,
        device_id: log.device_id,
        device_name: log.device_name,
        selected_app: log.selected_app || '默认',
        execution_status: log.execution_status,
        progress_percentage: log.progress || 0,
        execution_result: log.execution_result,
        execution_logs: log.execution_logs || '',
        error_message: log.error_message,
        started_at: log.started_at,
        completed_at: log.completed_at,
        execution_duration: log.execution_duration || 0,
        config_params: this.safeJsonParse(log.config_params, {}),
        schedule_config: this.safeJsonParse(log.schedule_config, null),

        // 格式化字段名（用于与闲鱼保持一致）
        id: log.task_id,
        taskId: log.task_id,
        functionType: log.function_type,
        functionName: log.function_name,
        selectedApp: log.selected_app || '默认',
        executionStatus: log.execution_status,
        progress: log.progress || 0,

        // 设备信息（与闲鱼格式保持一致）
        deviceInfo: {
          id: log.device_id,
          name: log.device_name || '未知设备',
          ip: '未知IP',
          selectedApp: log.selected_app || '默认'
        },

        // 配置参数
        configParams: this.safeJsonParse(log.config_params, {}),
        scheduleConfig: this.safeJsonParse(log.schedule_config, null),

        // 执行结果和日志
        executionResult: log.error_message ? '执行失败' :
          (log.execution_status === 'completed' ? '执行成功' : '执行中'),
        executionLogs: log.execution_logs || '',
        errorMessage: log.error_message,

        // 时间信息
        createdAt: log.started_at,
        startedAt: log.started_at,
        completedAt: log.completed_at,
        executionTime: log.execution_duration || 0,
        executionDuration: log.execution_duration || 0,

        // 兼容字段
        log_id: log.task_id,
        debug_logs: log.execution_logs || ''
      }));

      return {
        logs: processedLogs,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取小红书执行日志失败:', error);
      throw error;
    }
  }

  // 获取单个执行日志详情
  async getExecutionLogDetail(taskId) {
    try {
      const [logs] = await pool.execute(`
        SELECT *, selected_app FROM xiaohongshu_execution_logs WHERE task_id = ?
      `, [taskId]);

      if (logs.length === 0) {
        return null;
      }

      const log = logs[0];
      return {
        ...log,
        config_params: typeof log.config_params === 'string'
          ? JSON.parse(log.config_params)
          : log.config_params,
        schedule_config: log.schedule_config && typeof log.schedule_config === 'string'
          ? JSON.parse(log.schedule_config)
          : log.schedule_config,
        execution_result: log.execution_result && typeof log.execution_result === 'string'
          ? JSON.parse(log.execution_result)
          : log.execution_result
      };
    } catch (error) {
      console.error('获取小红书执行日志详情失败:', error);
      throw error;
    }
  }

  // 删除执行日志
  async deleteExecutionLog(taskId) {
    try {
      const [result] = await pool.execute(`
        DELETE FROM xiaohongshu_execution_logs WHERE task_id = ?
      `, [taskId]);

      console.log(`小红书执行日志已删除: ${taskId}`);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除小红书执行日志失败:', error);
      throw error;
    }
  }

  // 清空所有执行日志
  async clearAllExecutionLogs() {
    try {
      const [result] = await pool.execute(`
        DELETE FROM xiaohongshu_execution_logs
      `);

      console.log(`已清空所有小红书执行日志，删除了 ${result.affectedRows} 条记录`);
      return result.affectedRows;
    } catch (error) {
      console.error('清空小红书执行日志失败:', error);
      throw error;
    }
  }

  // 获取统计信息
  async getExecutionStats(filters = {}) {
    try {
      let whereClause = '';
      let params = [];

      // 构建筛选条件
      const conditions = [];

      if (filters.startDate) {
        conditions.push('started_at >= ?');
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        conditions.push('started_at <= ?');
        params.push(filters.endDate);
      }

      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }

      const [stats] = await pool.execute(`
        SELECT
          COUNT(*) as total_executions,
          SUM(CASE WHEN execution_status = 'completed' THEN 1 ELSE 0 END) as completed_count,
          SUM(CASE WHEN execution_status = 'failed' THEN 1 ELSE 0 END) as failed_count,
          SUM(CASE WHEN execution_status = 'running' THEN 1 ELSE 0 END) as running_count,
          AVG(execution_duration) as avg_duration,
          function_type,
          COUNT(*) as function_count
        FROM xiaohongshu_execution_logs
        ${whereClause}
        GROUP BY function_type
      `, params);

      return stats;
    } catch (error) {
      console.error('获取小红书执行统计失败:', error);
      throw error;
    }
  }

  // 批量更新所有正在执行的任务状态为已停止
  async updateAllRunningTasksToStopped(reason = '用户手动停止') {
    try {
      const [result] = await pool.execute(`
        UPDATE xiaohongshu_execution_logs
        SET execution_status = 'stopped',
            progress_percentage = 0,
            execution_logs = JSON_ARRAY_APPEND(IFNULL(execution_logs, JSON_ARRAY()), "$", ?),
            completed_at = NOW(),
            execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
        WHERE execution_status IN ('pending', 'running', 'executing')
      `, [JSON.stringify({
        timestamp: new Date().toISOString(),
        message: reason
      })]);

      console.log(`批量更新任务状态完成，影响行数: ${result.affectedRows}`);
      return result.affectedRows;
    } catch (error) {
      console.error('批量更新任务状态失败:', error);
      throw error;
    }
  }

  // 获取单个执行日志详情
  async getExecutionLogDetail(taskId) {
    try {
      const [rows] = await pool.execute(`
        SELECT
          id, task_id, function_type, device_id, device_name, function_name,
          config_params, schedule_config, execution_status,
          progress_percentage as progress, execution_result, execution_logs,
          error_message, started_at, completed_at, execution_duration
        FROM xiaohongshu_execution_logs
        WHERE task_id = ?
      `, [taskId]);

      if (rows.length === 0) {
        return null;
      }

      const log = rows[0];

      // 格式化为与闲鱼一致的格式
      return {
        id: log.task_id,
        taskId: log.task_id,
        functionType: log.function_type,
        functionName: log.function_name,
        executionStatus: log.execution_status,
        progress: log.progress || 0,

        deviceInfo: {
          id: log.device_id,
          name: log.device_name || '未知设备',
          ip: '未知IP'
        },

        configParams: this.safeJsonParse(log.config_params, {}),
        scheduleConfig: this.safeJsonParse(log.schedule_config, null),

        executionResult: log.error_message ? '执行失败' :
          (log.execution_status === 'completed' ? '执行成功' : '执行中'),
        executionLogs: log.execution_logs || '',
        errorMessage: log.error_message,

        createdAt: log.started_at,
        startedAt: log.started_at,
        completedAt: log.completed_at,
        executionTime: log.execution_duration || 0,
        executionDuration: log.execution_duration || 0
      };
    } catch (error) {
      console.error('获取小红书执行日志详情失败:', error);
      throw error;
    }
  }
}

module.exports = new XiaohongshuLogService();
